﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="demo_wfsummerModelStoreContainer" CdmEntityContainer="demo_wfsummerEntities">
    <EntitySetMapping Name="customers">
      <EntityTypeMapping TypeName="demo_wfsummerModel.customer">
        <MappingFragment StoreEntitySet="customers">
          <ScalarProperty Name="customer_id" ColumnName="customer_id" />
          <ScalarProperty Name="fullname" ColumnName="fullname" />
          <ScalarProperty Name="phone_number" ColumnName="phone_number" />
          <ScalarProperty Name="dob" ColumnName="dob" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="movie_attributes">
      <EntityTypeMapping TypeName="demo_wfsummerModel.movie_attributes">
        <MappingFragment StoreEntitySet="movie_attributes">
          <ScalarProperty Name="id" ColumnName="id" />
          <ScalarProperty Name="attribute_name" ColumnName="attribute_name" />
          <ScalarProperty Name="attribute_value" ColumnName="attribute_value" />
          <ScalarProperty Name="movie_id" ColumnName="movie_id" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="movie_types">
      <EntityTypeMapping TypeName="demo_wfsummerModel.movie_types">
        <MappingFragment StoreEntitySet="movie_types">
          <ScalarProperty Name="id" ColumnName="id" />
          <ScalarProperty Name="movie_id" ColumnName="movie_id" />
          <ScalarProperty Name="type_id" ColumnName="type_id" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="movies">
      <EntityTypeMapping TypeName="demo_wfsummerModel.movy">
        <MappingFragment StoreEntitySet="movies">
          <ScalarProperty Name="movie_id" ColumnName="movie_id" />
          <ScalarProperty Name="movie_name" ColumnName="movie_name" />
          <ScalarProperty Name="thumbnail_url" ColumnName="thumbnail_url" />
          <ScalarProperty Name="thumbnail_public_id" ColumnName="thumbnail_public_id" />
          <ScalarProperty Name="video_trailer_url" ColumnName="video_trailer_url" />
          <ScalarProperty Name="video_public_id" ColumnName="video_public_id" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
          <ScalarProperty Name="updated_at" ColumnName="updated_at" />
          <ScalarProperty Name="rate" ColumnName="rate" />
          <ScalarProperty Name="duration" ColumnName="duration" />
          <ScalarProperty Name="is_deleted" ColumnName="is_deleted" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="prices">
      <EntityTypeMapping TypeName="demo_wfsummerModel.price">
        <MappingFragment StoreEntitySet="prices">
          <ScalarProperty Name="price_id" ColumnName="price_id" />
          <ScalarProperty Name="movie_id" ColumnName="movie_id" />
          <ScalarProperty Name="is_vip" ColumnName="is_vip" />
          <ScalarProperty Name="price_value" ColumnName="price_value" />
          <ScalarProperty Name="ticket_type_id" ColumnName="ticket_type_id" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
          <ScalarProperty Name="updated_at" ColumnName="updated_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="room_sections">
      <EntityTypeMapping TypeName="demo_wfsummerModel.room_sections">
        <MappingFragment StoreEntitySet="room_sections">
          <ScalarProperty Name="section_id" ColumnName="section_id" />
          <ScalarProperty Name="section_name" ColumnName="section_name" />
          <ScalarProperty Name="room_id" ColumnName="room_id" />
          <ScalarProperty Name="num_rows" ColumnName="num_rows" />
          <ScalarProperty Name="seat_per_row" ColumnName="seat_per_row" />
          <ScalarProperty Name="row_num_vip" ColumnName="row_num_vip" />
          <ScalarProperty Name="start_row_num_vip" ColumnName="start_row_num_vip" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
          <ScalarProperty Name="updated_at" ColumnName="updated_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="rooms">
      <EntityTypeMapping TypeName="demo_wfsummerModel.room">
        <MappingFragment StoreEntitySet="rooms">
          <ScalarProperty Name="room_id" ColumnName="room_id" />
          <ScalarProperty Name="room_name" ColumnName="room_name" />
          <ScalarProperty Name="description" ColumnName="description" />
          <ScalarProperty Name="status" ColumnName="status" />
          <ScalarProperty Name="capacity" ColumnName="capacity" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
          <ScalarProperty Name="updated_at" ColumnName="updated_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="schedules">
      <EntityTypeMapping TypeName="demo_wfsummerModel.schedule">
        <MappingFragment StoreEntitySet="schedules">
          <ScalarProperty Name="schedule_id" ColumnName="schedule_id" />
          <ScalarProperty Name="room_id" ColumnName="room_id" />
          <ScalarProperty Name="movie_id" ColumnName="movie_id" />
          <ScalarProperty Name="show_time" ColumnName="show_time" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
          <ScalarProperty Name="updated_at" ColumnName="updated_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="sysdiagrams">
      <EntityTypeMapping TypeName="demo_wfsummerModel.sysdiagram">
        <MappingFragment StoreEntitySet="sysdiagrams">
          <ScalarProperty Name="name" ColumnName="name" />
          <ScalarProperty Name="principal_id" ColumnName="principal_id" />
          <ScalarProperty Name="diagram_id" ColumnName="diagram_id" />
          <ScalarProperty Name="version" ColumnName="version" />
          <ScalarProperty Name="definition" ColumnName="definition" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="tickets">
      <EntityTypeMapping TypeName="demo_wfsummerModel.ticket">
        <MappingFragment StoreEntitySet="tickets">
          <ScalarProperty Name="ticket_id" ColumnName="ticket_id" />
          <ScalarProperty Name="customer_id" ColumnName="customer_id" />
          <ScalarProperty Name="staff_id" ColumnName="staff_id" />
          <ScalarProperty Name="ticket_type_id" ColumnName="ticket_type_id" />
          <ScalarProperty Name="seat_label" ColumnName="seat_label" />
          <ScalarProperty Name="price" ColumnName="price" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
          <ScalarProperty Name="updated_at" ColumnName="updated_at" />
          <ScalarProperty Name="payment" ColumnName="payment" />
          <ScalarProperty Name="ispaid" ColumnName="ispaid" />
          <ScalarProperty Name="schedule_id" ColumnName="schedule_id" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="types">
      <EntityTypeMapping TypeName="demo_wfsummerModel.type">
        <MappingFragment StoreEntitySet="types">
          <ScalarProperty Name="type_id" ColumnName="type_id" />
          <ScalarProperty Name="type_name" ColumnName="type_name" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
          <ScalarProperty Name="updated_at" ColumnName="updated_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="users">
      <EntityTypeMapping TypeName="demo_wfsummerModel.user">
        <MappingFragment StoreEntitySet="users">
          <ScalarProperty Name="user_id" ColumnName="user_id" />
          <ScalarProperty Name="user_name" ColumnName="user_name" />
          <ScalarProperty Name="password" ColumnName="password" />
          <ScalarProperty Name="cccd" ColumnName="cccd" />
          <ScalarProperty Name="roleid" ColumnName="roleid" />
          <ScalarProperty Name="isdeleted" ColumnName="isdeleted" />
          <ScalarProperty Name="created_at" ColumnName="created_at" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <FunctionImportMapping FunctionImportName="sp_alterdiagram" FunctionName="demo_wfsummerModel.Store.sp_alterdiagram" />
    <FunctionImportMapping FunctionImportName="sp_creatediagram" FunctionName="demo_wfsummerModel.Store.sp_creatediagram" />
    <FunctionImportMapping FunctionImportName="sp_dropdiagram" FunctionName="demo_wfsummerModel.Store.sp_dropdiagram" />
    <FunctionImportMapping FunctionImportName="sp_helpdiagramdefinition" FunctionName="demo_wfsummerModel.Store.sp_helpdiagramdefinition">
      <ResultMapping>
        <ComplexTypeMapping TypeName="demo_wfsummerModel.sp_helpdiagramdefinition_Result">
          <ScalarProperty Name="version" ColumnName="version" />
          <ScalarProperty Name="definition" ColumnName="definition" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="sp_helpdiagrams" FunctionName="demo_wfsummerModel.Store.sp_helpdiagrams">
      <ResultMapping>
        <ComplexTypeMapping TypeName="demo_wfsummerModel.sp_helpdiagrams_Result">
          <ScalarProperty Name="Database" ColumnName="Database" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="ID" ColumnName="ID" />
          <ScalarProperty Name="Owner" ColumnName="Owner" />
          <ScalarProperty Name="OwnerID" ColumnName="OwnerID" />
        </ComplexTypeMapping>
      </ResultMapping>
    </FunctionImportMapping>
    <FunctionImportMapping FunctionImportName="sp_renamediagram" FunctionName="demo_wfsummerModel.Store.sp_renamediagram" />
    <FunctionImportMapping FunctionImportName="sp_upgraddiagrams" FunctionName="demo_wfsummerModel.Store.sp_upgraddiagrams" />
  </EntityContainerMapping>
</Mapping>