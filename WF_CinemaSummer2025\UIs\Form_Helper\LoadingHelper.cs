using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Helper class để tạo loading spinner có thể tái sử dụng
    /// </summary>
    public static class LoadingHelper
    {
        /// <summary>
        /// Tạo loading overlay cho form
        /// </summary>
        /// <param name="parentForm">Form cha</param>
        /// <param name="loadingText">Text hiển thị</param>
        /// <returns>Loading panel</returns>
        public static Guna2Panel CreateLoadingOverlay(Form parentForm, string loadingText = "Đang xử lý...")
        {
            // Tạo overlay panel che toàn bộ form
            var overlayPanel = new Guna2Panel
            {
                BackColor = Color.FromArgb(150, 0, 0, 0), // Semi-transparent black
                Dock = DockStyle.Fill,
                Visible = false,
                Name = "loadingOverlay"
            };

            // Tạo loading panel chính
            var loadingPanel = new Guna2Panel
            {
                BackColor = Color.White,
                BorderRadius = 15,
                BorderThickness = 1,
                BorderColor = Color.FromArgb(213, 218, 223),
                Size = new Size(250, 120),
                Name = "loadingPanel"
            };

            // Tạo spinner
            var spinner = new Guna2WinProgressIndicator
            {
                AutoStart = true,
                CircleSize = 1F,
                Location = new Point(100, 25),
                Size = new Size(50, 50),
                ProgressColor = Color.FromArgb(52, 152, 219),
                Name = "loadingSpinner"
            };

            // Tạo loading text
            var lblText = new Label
            {
                Text = loadingText,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(10, 85),
                Size = new Size(230, 25),
                TextAlign = ContentAlignment.MiddleCenter,
                Name = "loadingText"
            };

            // Thêm controls vào loading panel
            loadingPanel.Controls.Add(spinner);
            loadingPanel.Controls.Add(lblText);

            // Center loading panel trong overlay
            loadingPanel.Location = new Point(
                (overlayPanel.Width - loadingPanel.Width) / 2,
                (overlayPanel.Height - loadingPanel.Height) / 2
            );

            // Thêm loading panel vào overlay
            overlayPanel.Controls.Add(loadingPanel);

            // Thêm overlay vào form
            parentForm.Controls.Add(overlayPanel);
            overlayPanel.BringToFront();

            // Handle resize để center lại loading panel
            overlayPanel.Resize += (s, e) =>
            {
                loadingPanel.Location = new Point(
                    (overlayPanel.Width - loadingPanel.Width) / 2,
                    (overlayPanel.Height - loadingPanel.Height) / 2
                );
            };

            return overlayPanel;
        }

        /// <summary>
        /// Hiển thị loading overlay
        /// </summary>
        /// <param name="overlayPanel">Loading overlay panel</param>
        /// <param name="loadingText">Text hiển thị</param>
        /// <param name="disableForm">Có disable form không</param>
        public static void ShowLoading(Guna2Panel overlayPanel, string loadingText = null, bool disableForm = true)
        {
            if (overlayPanel == null) return;

            // Update text nếu có
            if (!string.IsNullOrEmpty(loadingText))
            {
                var lblText = overlayPanel.Controls.Find("loadingText", true);
                if (lblText.Length > 0 && lblText[0] is Label label)
                {
                    label.Text = loadingText;
                }
            }

            // Disable form controls nếu cần
            if (disableForm && overlayPanel.Parent is Form parentForm)
            {
                DisableFormControls(parentForm, overlayPanel);
            }

            // Hiển thị overlay
            overlayPanel.Visible = true;
            overlayPanel.BringToFront();

            // Start spinner
            var spinner = overlayPanel.Controls.Find("loadingSpinner", true);
            if (spinner.Length > 0 && spinner[0] is Guna2WinProgressIndicator progressIndicator)
            {
                progressIndicator.Start();
            }
        }

        /// <summary>
        /// Ẩn loading overlay
        /// </summary>
        /// <param name="overlayPanel">Loading overlay panel</param>
        /// <param name="enableForm">Có enable lại form không</param>
        public static void HideLoading(Guna2Panel overlayPanel, bool enableForm = true)
        {
            if (overlayPanel == null) return;

            // Stop spinner
            var spinner = overlayPanel.Controls.Find("loadingSpinner", true);
            if (spinner.Length > 0 && spinner[0] is Guna2WinProgressIndicator progressIndicator)
            {
                progressIndicator.Stop();
            }

            // Ẩn overlay
            overlayPanel.Visible = false;

            // Enable lại form controls nếu cần
            if (enableForm && overlayPanel.Parent is Form parentForm)
            {
                EnableFormControls(parentForm, overlayPanel);
            }
        }

        /// <summary>
        /// Update loading text
        /// </summary>
        /// <param name="overlayPanel">Loading overlay panel</param>
        /// <param name="newText">Text mới</param>
        public static void UpdateLoadingText(Guna2Panel overlayPanel, string newText)
        {
            if (overlayPanel == null || !overlayPanel.Visible) return;

            var lblText = overlayPanel.Controls.Find("loadingText", true);
            if (lblText.Length > 0 && lblText[0] is Label label)
            {
                label.Text = newText;
            }
        }

        /// <summary>
        /// Thực hiện task với loading
        /// </summary>
        /// <param name="overlayPanel">Loading overlay panel</param>
        /// <param name="task">Task cần thực hiện</param>
        /// <param name="loadingText">Loading text</param>
        /// <param name="successText">Success text (hiển thị trong 800ms)</param>
        /// <returns></returns>
        public static async Task ExecuteWithLoadingAsync(Guna2Panel overlayPanel, Func<Task> task, 
            string loadingText = "Đang xử lý...", string successText = "Hoàn thành!")
        {
            try
            {
                ShowLoading(overlayPanel, loadingText);
                
                await task();
                
                if (!string.IsNullOrEmpty(successText))
                {
                    UpdateLoadingText(overlayPanel, successText);
                    await Task.Delay(800);
                }
            }
            finally
            {
                HideLoading(overlayPanel);
            }
        }

        /// <summary>
        /// Thực hiện task với loading và trả về kết quả
        /// </summary>
        /// <typeparam name="T">Kiểu dữ liệu trả về</typeparam>
        /// <param name="overlayPanel">Loading overlay panel</param>
        /// <param name="task">Task cần thực hiện</param>
        /// <param name="loadingText">Loading text</param>
        /// <param name="successText">Success text</param>
        /// <returns>Kết quả của task</returns>
        public static async Task<T> ExecuteWithLoadingAsync<T>(Guna2Panel overlayPanel, Func<Task<T>> task,
            string loadingText = "Đang xử lý...", string successText = "Hoàn thành!")
        {
            try
            {
                ShowLoading(overlayPanel, loadingText);
                
                var result = await task();
                
                if (!string.IsNullOrEmpty(successText))
                {
                    UpdateLoadingText(overlayPanel, successText);
                    await Task.Delay(800);
                }
                
                return result;
            }
            finally
            {
                HideLoading(overlayPanel);
            }
        }

        private static void DisableFormControls(Form form, Control excludeControl)
        {
            foreach (Control control in form.Controls)
            {
                if (control != excludeControl)
                {
                    control.Enabled = false;
                }
            }
        }

        private static void EnableFormControls(Form form, Control excludeControl)
        {
            foreach (Control control in form.Controls)
            {
                if (control != excludeControl)
                {
                    control.Enabled = true;
                }
            }
        }
    }
}
