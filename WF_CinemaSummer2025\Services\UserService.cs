﻿using System;
using System.Linq;
using WF_CinemaSummer2025.Repositories;
using BCrypt.Net;

namespace WF_CinemaSummer2025.Services
{
    public class UserService
    {
        private demo_wfsummerEntities db;

        public UserService()
        {
            db = new demo_wfsummerEntities();
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> thực đăng nhập
        /// </summary>
        /// <param name="username">Tên đăng nhập</param>
        /// <param name="password">Mật khẩu</param>
        /// <returns>User nếu đăng nhập thành công, null nếu thất bại</returns>
        public user Login(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                    return null;

                var user = db.users.FirstOrDefault(u => u.user_name == username && u.isdeleted != true);
                
                if (user != null && BCrypt.Net.BCrypt.Verify(password, user.password))
                {
                    return user;
                }

                return null;
            }
            catch (Exception ex)
            {
                // Log error here if needed
                return null;
            }
        }

        /// <summary>
        /// Kiểm tra role của user
        /// </summary>
        /// <param name="user">User object</param>
        /// <returns>String mô tả role</returns>
        public string GetUserRole(user user)
        {
            if (user == null) return "Unknown";
            
            switch (user.roleid)
            {
                case 0:
                    return "Admin";
                case 1:
                    return "Staff";
                default:
                    return "Unknown";
            }
        }

        /// <summary>
        /// Kiểm tra user có phải admin không
        /// </summary>
        /// <param name="user">User object</param>
        /// <returns>True nếu là admin</returns>
        public bool IsAdmin(user user)
        {
            return user != null && user.roleid == 0;
        }

        /// <summary>
        /// Kiểm tra user có phải staff không
        /// </summary>
        /// <param name="user">User object</param>
        /// <returns>True nếu là staff</returns>
        public bool IsStaff(user user)
        {
            return user != null && user.roleid == 1;
        }

        public void Dispose()
        {
            db?.Dispose();
        }
    }
}
