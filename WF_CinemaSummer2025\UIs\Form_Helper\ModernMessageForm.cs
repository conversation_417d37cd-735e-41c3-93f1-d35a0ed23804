using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Modern Message Form theo thiết kế trong hình với icon tròn và layout đẹp
    /// </summary>
    public partial class ModernMessageForm : Form
    {
        // Enum để định nghĩa loại message
        public enum MessageType
        {
            Information,    // Thông báo thông thường
            Success,        // Thành công
            Warning,        // Cảnh báo
            Error,          // Lỗi
            Question        // Câu hỏi xác nhận
        }

        // Enum để định nghĩa kết quả trả về
        public enum MessageResult
        {
            Yes,
            No,
            OK,
            None
        }

        // Property để lưu kết quả người dùng chọn
        public MessageResult Result { get; private set; } = MessageResult.None;

        private MessageType _messageType = MessageType.Warning;

        // Constructor mặc định
        public ModernMessageForm()
        {
            InitializeComponent();
            SetupForm();
        }

        // Constructor với message tùy chỉnh
        public ModernMessageForm(string message, MessageType type = MessageType.Warning) : this()
        {
            SetMessage(message, type);
        }

        // Constructor với message và title tùy chỉnh
        public ModernMessageForm(string message, string title, MessageType type = MessageType.Warning) : this(message, type)
        {
            this.Text = title;
        }

        // Phương thức để thiết lập form
        private void SetupForm()
        {
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.TopMost = true;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.TransparencyKey = Color.FromArgb(240, 240, 240);
        }

        // Phương thức để set message và type
        public void SetMessage(string message, MessageType type = MessageType.Warning)
        {
            lblMessage.Text = message;
            _messageType = type;
            SetupMessageType(type);
        }

        // Phương thức để thiết lập giao diện theo loại message
        private void SetupMessageType(MessageType type)
        {
            switch (type)
            {
                case MessageType.Success:
                    SetupSuccessStyle();
                    break;
                case MessageType.Warning:
                    SetupWarningStyle();
                    break;
                case MessageType.Error:
                    SetupErrorStyle();
                    break;
                case MessageType.Question:
                    SetupQuestionStyle();
                    break;
                default:
                    SetupInformationStyle();
                    break;
            }
        }

        private void SetupSuccessStyle()
        {
            // Icon xanh lá với checkmark
            iconPanel.BackColor = Color.FromArgb(40, 167, 69);
            lblIcon.Text = "✓";
            lblIcon.ForeColor = Color.White;
            lblTitle.Text = "Thành công";
            
            // Chỉ hiển thị nút OK cho success
            btnYes.Visible = false;
            btnNo.Visible = false;
            btnOK.Visible = true;
            btnOK.Text = "OK";
            CenterSingleButton();
        }

        private void SetupWarningStyle()
        {
            // Icon vàng với dấu cảnh báo
            iconPanel.BackColor = Color.FromArgb(255, 193, 7);
            lblIcon.Text = "!";
            lblIcon.ForeColor = Color.White;
            lblTitle.Text = "Xác nhận Hành động";
            
            // Hiển thị cả hai nút cho warning
            btnYes.Visible = true;
            btnNo.Visible = true;
            btnOK.Visible = false;
            btnNo.Text = "Không";
            btnYes.Text = "Có, Tôi chắc chắn";
            PositionTwoButtons();
        }

        private void SetupErrorStyle()
        {
            // Icon đỏ với dấu X
            iconPanel.BackColor = Color.FromArgb(220, 53, 69);
            lblIcon.Text = "✕";
            lblIcon.ForeColor = Color.White;
            lblTitle.Text = "Lỗi";
            
            // Chỉ hiển thị nút OK cho error
            btnYes.Visible = false;
            btnNo.Visible = false;
            btnOK.Visible = true;
            btnOK.Text = "OK";
            CenterSingleButton();
        }

        private void SetupQuestionStyle()
        {
            // Icon xanh dương với dấu hỏi
            iconPanel.BackColor = Color.FromArgb(52, 152, 219);
            lblIcon.Text = "?";
            lblIcon.ForeColor = Color.White;
            lblTitle.Text = "Xác nhận";
            
            // Hiển thị cả hai nút cho question
            btnYes.Visible = true;
            btnNo.Visible = true;
            btnOK.Visible = false;
            btnNo.Text = "Không";
            btnYes.Text = "Có";
            PositionTwoButtons();
        }

        private void SetupInformationStyle()
        {
            // Icon xanh dương với dấu i
            iconPanel.BackColor = Color.FromArgb(23, 162, 184);
            lblIcon.Text = "i";
            lblIcon.ForeColor = Color.White;
            lblTitle.Text = "Thông báo";
            
            // Chỉ hiển thị nút OK cho information
            btnYes.Visible = false;
            btnNo.Visible = false;
            btnOK.Visible = true;
            btnOK.Text = "OK";
            CenterSingleButton();
        }

        private void CenterSingleButton()
        {
            btnOK.Location = new Point((this.Width - btnOK.Width) / 2, btnOK.Location.Y);
        }

        private void PositionTwoButtons()
        {
            int spacing = 20;
            int totalWidth = btnNo.Width + btnYes.Width + spacing;
            int startX = (this.Width - totalWidth) / 2;
            
            btnNo.Location = new Point(startX, btnNo.Location.Y);
            btnYes.Location = new Point(startX + btnNo.Width + spacing, btnYes.Location.Y);
        }

        // Event handlers
        private void btnYes_Click(object sender, EventArgs e)
        {
            Result = MessageResult.Yes;
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        private void btnNo_Click(object sender, EventArgs e)
        {
            Result = MessageResult.No;
            this.DialogResult = DialogResult.No;
            this.Close();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            Result = MessageResult.OK;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        // Static methods để sử dụng dễ dàng
        public static MessageResult ShowSuccess(string message, string title = "Thành công")
        {
            using (var form = new ModernMessageForm(message, title, MessageType.Success))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowError(string message, string title = "Lỗi")
        {
            using (var form = new ModernMessageForm(message, title, MessageType.Error))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowWarning(string message, string title = "Cảnh báo")
        {
            using (var form = new ModernMessageForm(message, title, MessageType.Warning))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowQuestion(string message, string title = "Xác nhận")
        {
            using (var form = new ModernMessageForm(message, title, MessageType.Question))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowInformation(string message, string title = "Thông báo")
        {
            using (var form = new ModernMessageForm(message, title, MessageType.Information))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        // Overloads với parent form
        public static MessageResult ShowWarning(IWin32Window owner, string message, string title = "Cảnh báo")
        {
            using (var form = new ModernMessageForm(message, title, MessageType.Warning))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static MessageResult ShowQuestion(IWin32Window owner, string message, string title = "Xác nhận")
        {
            using (var form = new ModernMessageForm(message, title, MessageType.Question))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            if (_messageType == MessageType.Warning || _messageType == MessageType.Question)
            {
                PositionTwoButtons();
            }
            else
            {
                CenterSingleButton();
            }
        }
    }
}
