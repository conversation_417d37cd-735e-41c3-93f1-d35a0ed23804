using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    public partial class errorForm : Form
    {
        // Enum để định nghĩa loại error
        public enum ErrorType
        {
            Error,          // Lỗi nghiêm trọng
            Warning,        // Cảnh báo
            Information,    // Thông tin
            Critical        // Lỗi nghiêm trọng cần chú ý
        }

        // Enum để định nghĩa kết quả trả về
        public enum ErrorResult
        {
            OK,
            Retry,
            Cancel,
            None
        }

        // Property để lưu kết quả người dùng chọn
        public ErrorResult Result { get; private set; } = ErrorResult.None;

        private ErrorType _errorType = ErrorType.Error;

        // Constructor mặc định
        public errorForm()
        {
            InitializeComponent();
            SetupForm();
        }

        // Constructor với message tùy chỉnh
        public errorForm(string message, ErrorType type = ErrorType.Error) : this()
        {
            SetError(message, type);
        }

        // Constructor với message, title và type tùy chỉnh
        public errorForm(string message, string title, ErrorType type = ErrorType.Error) : this(message, type)
        {
            this.Text = title;
        }

        // Phương thức để thiết lập form
        private void SetupForm()
        {
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // Thêm shadow effect
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.TransparencyKey = Color.FromArgb(240, 240, 240);
        }

        // Phương thức để set error message và type
        public void SetError(string message, ErrorType type = ErrorType.Error)
        {
            lblErrorMessage.Text = message;
            _errorType = type;
            SetupErrorType(type);
        }

        // Phương thức để thiết lập giao diện theo loại error
        private void SetupErrorType(ErrorType type)
        {
            switch (type)
            {
                case ErrorType.Warning:
                    SetupWarningStyle();
                    break;
                case ErrorType.Information:
                    SetupInformationStyle();
                    break;
                case ErrorType.Critical:
                    SetupCriticalStyle();
                    break;
                default:
                    SetupErrorStyle();
                    break;
            }
        }

        private void SetupErrorStyle()
        {
            panelHeader.BackColor = Color.FromArgb(220, 53, 69);
            lblTitle.Text = "✕ Lỗi";
            lblTitle.ForeColor = Color.White;
            iconPanel.BackColor = Color.FromArgb(220, 53, 69);

            // Chỉ hiển thị nút OK cho error
            btnOK.Visible = true;
            btnRetry.Visible = false;
            btnCancel.Visible = false;
            btnOK.Location = new Point(175, 170); // Center the OK button
        }

        private void SetupWarningStyle()
        {
            panelHeader.BackColor = Color.FromArgb(255, 193, 7);
            lblTitle.Text = "⚠ Cảnh báo";
            lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            iconPanel.BackColor = Color.FromArgb(255, 193, 7);

            // Hiển thị OK và Cancel cho warning
            btnOK.Visible = true;
            btnRetry.Visible = false;
            btnCancel.Visible = true;
            btnOK.Location = new Point(140, 170);
            btnCancel.Location = new Point(250, 170);
        }

        private void SetupInformationStyle()
        {
            panelHeader.BackColor = Color.FromArgb(23, 162, 184);
            lblTitle.Text = "ℹ Thông tin";
            lblTitle.ForeColor = Color.White;
            iconPanel.BackColor = Color.FromArgb(23, 162, 184);

            // Chỉ hiển thị nút OK cho information
            btnOK.Visible = true;
            btnRetry.Visible = false;
            btnCancel.Visible = false;
            btnOK.Location = new Point(175, 170); // Center the OK button
        }

        private void SetupCriticalStyle()
        {
            panelHeader.BackColor = Color.FromArgb(139, 0, 0);
            lblTitle.Text = "🚨 Lỗi nghiêm trọng";
            lblTitle.ForeColor = Color.White;
            iconPanel.BackColor = Color.FromArgb(139, 0, 0);

            // Hiển thị Retry và Cancel cho critical error
            btnOK.Visible = false;
            btnRetry.Visible = true;
            btnCancel.Visible = true;
            btnRetry.Location = new Point(140, 170);
            btnCancel.Location = new Point(250, 170);
        }

        // Event handlers
        private void btnOK_Click(object sender, EventArgs e)
        {
            Result = ErrorResult.OK;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnRetry_Click(object sender, EventArgs e)
        {
            Result = ErrorResult.Retry;
            this.DialogResult = DialogResult.Retry;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            Result = ErrorResult.Cancel;
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        // Static methods để sử dụng dễ dàng
        public static ErrorResult ShowError(string message, string title = "Lỗi")
        {
            using (var form = new errorForm(message, title, ErrorType.Error))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static ErrorResult ShowWarning(string message, string title = "Cảnh báo")
        {
            using (var form = new errorForm(message, title, ErrorType.Warning))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static ErrorResult ShowInformation(string message, string title = "Thông tin")
        {
            using (var form = new errorForm(message, title, ErrorType.Information))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static ErrorResult ShowCritical(string message, string title = "Lỗi nghiêm trọng")
        {
            using (var form = new errorForm(message, title, ErrorType.Critical))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        // Overloads với parent form
        public static ErrorResult ShowError(IWin32Window owner, string message, string title = "Lỗi")
        {
            using (var form = new errorForm(message, title, ErrorType.Error))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static ErrorResult ShowWarning(IWin32Window owner, string message, string title = "Cảnh báo")
        {
            using (var form = new errorForm(message, title, ErrorType.Warning))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static ErrorResult ShowInformation(IWin32Window owner, string message, string title = "Thông tin")
        {
            using (var form = new errorForm(message, title, ErrorType.Information))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static ErrorResult ShowCritical(IWin32Window owner, string message, string title = "Lỗi nghiêm trọng")
        {
            using (var form = new errorForm(message, title, ErrorType.Critical))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        // Phương thức để hiển thị error với exception details
        public static ErrorResult ShowException(Exception ex, string title = "Lỗi hệ thống")
        {
            string message = $"Đã xảy ra lỗi: {ex.Message}";
            if (ex.InnerException != null)
            {
                message += $"\n\nChi tiết: {ex.InnerException.Message}";
            }

            using (var form = new errorForm(message, title, ErrorType.Critical))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static ErrorResult ShowException(IWin32Window owner, Exception ex, string title = "Lỗi hệ thống")
        {
            string message = $"Đã xảy ra lỗi: {ex.Message}";
            if (ex.InnerException != null)
            {
                message += $"\n\nChi tiết: {ex.InnerException.Message}";
            }

            using (var form = new errorForm(message, title, ErrorType.Critical))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }
    }
}
