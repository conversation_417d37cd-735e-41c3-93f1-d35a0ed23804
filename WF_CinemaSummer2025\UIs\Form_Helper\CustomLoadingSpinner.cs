using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Custom Loading Spinner với hiệu ứng đẹp mắt
    /// </summary>
    public partial class CustomLoadingSpinner : UserControl
    {
        private Timer animationTimer;
        private float rotationAngle = 0f;
        private Color primaryColor = Color.FromArgb(52, 152, 219);
        private Color secondaryColor = Color.FromArgb(230, 230, 230);
        private int dotCount = 12;
        private float dotSize = 3f;
        private bool isAnimating = false;

        public CustomLoadingSpinner()
        {
            InitializeComponent();
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);
            
            // Initialize timer
            animationTimer = new Timer();
            animationTimer.Interval = 80; // 80ms for smooth animation
            animationTimer.Tick += AnimationTimer_Tick;
            
            // Set default size
            this.Size = new Size(60, 60);
            this.BackColor = Color.Transparent;
        }

        #region Properties

        /// <summary>
        /// Màu chính của spinner
        /// </summary>
        public Color PrimaryColor
        {
            get { return primaryColor; }
            set
            {
                primaryColor = value;
                Invalidate();
            }
        }

        /// <summary>
        /// Màu phụ của spinner
        /// </summary>
        public Color SecondaryColor
        {
            get { return secondaryColor; }
            set
            {
                secondaryColor = value;
                Invalidate();
            }
        }

        /// <summary>
        /// Số lượng dots
        /// </summary>
        public int DotCount
        {
            get { return dotCount; }
            set
            {
                dotCount = Math.Max(6, Math.Min(20, value));
                Invalidate();
            }
        }

        /// <summary>
        /// Kích thước của dots
        /// </summary>
        public float DotSize
        {
            get { return dotSize; }
            set
            {
                dotSize = Math.Max(1f, Math.Min(10f, value));
                Invalidate();
            }
        }

        /// <summary>
        /// Kiểm tra xem spinner có đang chạy không
        /// </summary>
        public bool IsAnimating
        {
            get { return isAnimating; }
        }

        #endregion

        #region Methods

        /// <summary>
        /// Bắt đầu animation
        /// </summary>
        public void Start()
        {
            if (!isAnimating)
            {
                isAnimating = true;
                animationTimer.Start();
            }
        }

        /// <summary>
        /// Dừng animation
        /// </summary>
        public void Stop()
        {
            if (isAnimating)
            {
                isAnimating = false;
                animationTimer.Stop();
                rotationAngle = 0f;
                Invalidate();
            }
        }

        #endregion

        #region Event Handlers

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            rotationAngle += 30f; // Rotate 30 degrees each tick
            if (rotationAngle >= 360f)
                rotationAngle = 0f;
            
            Invalidate(); // Trigger repaint
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            
            // Calculate center and radius
            PointF center = new PointF(Width / 2f, Height / 2f);
            float radius = Math.Min(Width, Height) / 2f - dotSize * 2;
            
            // Draw dots
            for (int i = 0; i < dotCount; i++)
            {
                float angle = (360f / dotCount * i + rotationAngle) * (float)Math.PI / 180f;
                
                // Calculate dot position
                float x = center.X + radius * (float)Math.Cos(angle);
                float y = center.Y + radius * (float)Math.Sin(angle);
                
                // Calculate alpha based on position (fade effect)
                float alpha = isAnimating ? GetAlphaForDot(i) : 0.3f;
                
                // Create brush with calculated alpha
                Color dotColor = Color.FromArgb(
                    (int)(255 * alpha),
                    primaryColor.R,
                    primaryColor.G,
                    primaryColor.B
                );
                
                using (SolidBrush brush = new SolidBrush(dotColor))
                {
                    float dotDiameter = dotSize * 2;
                    g.FillEllipse(brush, x - dotSize, y - dotSize, dotDiameter, dotDiameter);
                }
            }
        }

        private float GetAlphaForDot(int dotIndex)
        {
            // Calculate which dot should be brightest based on rotation
            float normalizedRotation = (rotationAngle % 360f) / 360f;
            float dotPosition = (float)dotIndex / dotCount;
            
            // Calculate distance from the "active" position
            float distance = Math.Abs(normalizedRotation - dotPosition);
            if (distance > 0.5f)
                distance = 1f - distance;
            
            // Convert distance to alpha (closer = brighter)
            float alpha = 1f - (distance * 2f);
            return Math.Max(0.1f, Math.Min(1f, alpha));
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            Invalidate();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                animationTimer?.Stop();
                animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion

        #region Designer Code

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // CustomLoadingSpinner
            // 
            this.Name = "CustomLoadingSpinner";
            this.Size = new System.Drawing.Size(60, 60);
            this.ResumeLayout(false);
        }

        #endregion
    }

    /// <summary>
    /// Loading Panel với custom spinner và text
    /// </summary>
    public partial class CustomLoadingPanel : UserControl
    {
        private CustomLoadingSpinner spinner;
        private Label loadingLabel;
        private string loadingText = "Đang xử lý...";

        public CustomLoadingPanel()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupPanel();
        }

        private void InitializeCustomComponents()
        {
            // Create spinner
            spinner = new CustomLoadingSpinner
            {
                Size = new Size(50, 50),
                PrimaryColor = Color.FromArgb(52, 152, 219),
                DotCount = 12,
                DotSize = 2.5f
            };

            // Create label
            loadingLabel = new Label
            {
                Text = loadingText,
                Font = new Font("Segoe UI", 11F, FontStyle.Regular),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                AutoSize = false
            };

            // Add to panel
            this.Controls.Add(spinner);
            this.Controls.Add(loadingLabel);
        }

        private void SetupPanel()
        {
            this.BackColor = Color.White;
            this.Size = new Size(250, 140);
            this.Visible = false;

            // Add shadow effect
            this.BorderStyle = BorderStyle.None;

            // Position controls
            PositionControls();
        }

        private void PositionControls()
        {
            // Center spinner
            spinner.Location = new Point(
                (Width - spinner.Width) / 2,
                30
            );

            // Position label below spinner
            loadingLabel.Size = new Size(Width - 20, 30);
            loadingLabel.Location = new Point(10, 95);
        }

        public string LoadingText
        {
            get { return loadingText; }
            set
            {
                loadingText = value;
                if (loadingLabel != null)
                    loadingLabel.Text = value;
            }
        }

        public void StartLoading(string text = null)
        {
            if (!string.IsNullOrEmpty(text))
                LoadingText = text;

            spinner?.Start();
            this.Visible = true;
            this.BringToFront();
        }

        public void StopLoading()
        {
            spinner?.Stop();
            this.Visible = false;
        }

        protected override void OnResize(EventArgs eventargs)
        {
            base.OnResize(eventargs);
            if (spinner != null && loadingLabel != null)
                PositionControls();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                spinner?.Stop();
                spinner?.Dispose();
                loadingLabel?.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer Code

        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // CustomLoadingPanel
            //
            this.BackColor = System.Drawing.Color.White;
            this.Name = "CustomLoadingPanel";
            this.Size = new System.Drawing.Size(250, 140);
            this.ResumeLayout(false);
        }

        #endregion
    }
}
