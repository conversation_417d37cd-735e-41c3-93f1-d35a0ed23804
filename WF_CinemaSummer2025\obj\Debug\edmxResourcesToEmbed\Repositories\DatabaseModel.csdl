﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="demo_wfsummerModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
  <EntityType Name="customer">
    <Key>
      <PropertyRef Name="customer_id" />
    </Key>
    <Property Name="customer_id" Type="Guid" Nullable="false" />
    <Property Name="fullname" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="phone_number" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
    <Property Name="dob" Type="DateTime" Precision="0" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="tickets" Relationship="Self.FK_tickets_customer" FromRole="customers" ToRole="tickets" />
  </EntityType>
  <EntityType Name="movie_attributes">
    <Key>
      <PropertyRef Name="id" />
    </Key>
    <Property Name="id" Type="Guid" Nullable="false" />
    <Property Name="attribute_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="attribute_value" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="movie_id" Type="Guid" Nullable="false" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="movy" Relationship="Self.FK_movie_attributes_movie" FromRole="movie_attributes" ToRole="movies" />
  </EntityType>
  <EntityType Name="movie_types">
    <Key>
      <PropertyRef Name="id" />
    </Key>
    <Property Name="id" Type="Guid" Nullable="false" />
    <Property Name="movie_id" Type="Guid" Nullable="false" />
    <Property Name="type_id" Type="Guid" Nullable="false" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="movy" Relationship="Self.FK_movie_types_movie" FromRole="movie_types" ToRole="movies" />
    <NavigationProperty Name="type" Relationship="Self.FK_movie_types_type" FromRole="movie_types" ToRole="types" />
  </EntityType>
  <EntityType Name="movy">
    <Key>
      <PropertyRef Name="movie_id" />
    </Key>
    <Property Name="movie_id" Type="Guid" Nullable="false" />
    <Property Name="movie_name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="thumbnail_url" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="thumbnail_public_id" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="video_trailer_url" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="video_public_id" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <Property Name="updated_at" Type="DateTime" Precision="3" />
    <Property Name="rate" Type="Decimal" Precision="3" Scale="1" />
    <Property Name="duration" Type="Int32" Nullable="false" />
    <Property Name="is_deleted" Type="Boolean" />
    <NavigationProperty Name="movie_attributes" Relationship="Self.FK_movie_attributes_movie" FromRole="movies" ToRole="movie_attributes" />
    <NavigationProperty Name="movie_types" Relationship="Self.FK_movie_types_movie" FromRole="movies" ToRole="movie_types" />
    <NavigationProperty Name="prices" Relationship="Self.FK_prices_movie" FromRole="movies" ToRole="prices" />
    <NavigationProperty Name="schedules" Relationship="Self.FK_schedules_movie" FromRole="movies" ToRole="schedules" />
  </EntityType>
  <EntityType Name="price">
    <Key>
      <PropertyRef Name="price_id" />
    </Key>
    <Property Name="price_id" Type="Guid" Nullable="false" />
    <Property Name="movie_id" Type="Guid" Nullable="false" />
    <Property Name="is_vip" Type="Boolean" Nullable="false" />
    <Property Name="price_value" Type="Decimal" Precision="10" Scale="2" Nullable="false" />
    <Property Name="ticket_type_id" Type="Int32" Nullable="false" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <Property Name="updated_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="movy" Relationship="Self.FK_prices_movie" FromRole="prices" ToRole="movies" />
  </EntityType>
  <EntityType Name="room_sections">
    <Key>
      <PropertyRef Name="section_id" />
    </Key>
    <Property Name="section_id" Type="Guid" Nullable="false" />
    <Property Name="section_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="room_id" Type="Guid" Nullable="false" />
    <Property Name="num_rows" Type="Int32" Nullable="false" />
    <Property Name="seat_per_row" Type="Int32" Nullable="false" />
    <Property Name="row_num_vip" Type="Int32" />
    <Property Name="start_row_num_vip" Type="Int32" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <Property Name="updated_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="room" Relationship="Self.FK_room_sections_room" FromRole="room_sections" ToRole="rooms" />
  </EntityType>
  <EntityType Name="room">
    <Key>
      <PropertyRef Name="room_id" />
    </Key>
    <Property Name="room_id" Type="Guid" Nullable="false" />
    <Property Name="room_name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="description" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
    <Property Name="status" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="capacity" Type="Int32" Nullable="false" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <Property Name="updated_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="room_sections" Relationship="Self.FK_room_sections_room" FromRole="rooms" ToRole="room_sections" />
    <NavigationProperty Name="schedules" Relationship="Self.FK_schedules_room" FromRole="rooms" ToRole="schedules" />
  </EntityType>
  <EntityType Name="schedule">
    <Key>
      <PropertyRef Name="schedule_id" />
    </Key>
    <Property Name="schedule_id" Type="Guid" Nullable="false" />
    <Property Name="room_id" Type="Guid" Nullable="false" />
    <Property Name="movie_id" Type="Guid" Nullable="false" />
    <Property Name="show_time" Type="DateTime" Nullable="false" Precision="7" />
    <Property Name="created_at" Type="DateTime" Precision="7" />
    <Property Name="updated_at" Type="DateTime" Precision="7" />
    <NavigationProperty Name="movy" Relationship="Self.FK_schedules_movie" FromRole="schedules" ToRole="movies" />
    <NavigationProperty Name="room" Relationship="Self.FK_schedules_room" FromRole="schedules" ToRole="rooms" />
    <NavigationProperty Name="tickets" Relationship="Self.FK_tickets_schedules" FromRole="schedules" ToRole="tickets" />
  </EntityType>
  <EntityType Name="sysdiagram">
    <Key>
      <PropertyRef Name="diagram_id" />
    </Key>
    <Property Name="name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="principal_id" Type="Int32" Nullable="false" />
    <Property Name="diagram_id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
    <Property Name="version" Type="Int32" />
    <Property Name="definition" Type="Binary" MaxLength="Max" FixedLength="false" />
  </EntityType>
  <EntityType Name="ticket">
    <Key>
      <PropertyRef Name="ticket_id" />
    </Key>
    <Property Name="ticket_id" Type="String" MaxLength="12" FixedLength="true" Unicode="false" Nullable="false" />
    <Property Name="customer_id" Type="Guid" />
    <Property Name="staff_id" Type="String" MaxLength="8" FixedLength="true" Unicode="false" Nullable="false" />
    <Property Name="ticket_type_id" Type="Int32" Nullable="false" />
    <Property Name="seat_label" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="price" Type="Decimal" Precision="10" Scale="2" Nullable="false" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <Property Name="updated_at" Type="DateTime" Precision="3" />
    <Property Name="payment" Type="Int32" Nullable="false" />
    <Property Name="ispaid" Type="Boolean" />
    <Property Name="schedule_id" Type="Guid" Nullable="false" />
    <NavigationProperty Name="customer" Relationship="Self.FK_tickets_customer" FromRole="tickets" ToRole="customers" />
    <NavigationProperty Name="schedule" Relationship="Self.FK_tickets_schedules" FromRole="tickets" ToRole="schedules" />
    <NavigationProperty Name="user" Relationship="Self.FK_tickets_staff" FromRole="tickets" ToRole="users" />
  </EntityType>
  <EntityType Name="type">
    <Key>
      <PropertyRef Name="type_id" />
    </Key>
    <Property Name="type_id" Type="Guid" Nullable="false" />
    <Property Name="type_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <Property Name="updated_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="movie_types" Relationship="Self.FK_movie_types_type" FromRole="types" ToRole="movie_types" />
  </EntityType>
  <EntityType Name="user">
    <Key>
      <PropertyRef Name="user_id" />
    </Key>
    <Property Name="user_id" Type="String" MaxLength="8" FixedLength="true" Unicode="false" Nullable="false" />
    <Property Name="user_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="password" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
    <Property Name="cccd" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
    <Property Name="roleid" Type="Int32" Nullable="false" />
    <Property Name="isdeleted" Type="Boolean" />
    <Property Name="created_at" Type="DateTime" Precision="3" />
    <NavigationProperty Name="tickets" Relationship="Self.FK_tickets_staff" FromRole="users" ToRole="tickets" />
  </EntityType>
  <Association Name="FK_tickets_customer">
    <End Role="customers" Type="Self.customer" Multiplicity="0..1" />
    <End Role="tickets" Type="Self.ticket" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="customers">
        <PropertyRef Name="customer_id" />
      </Principal>
      <Dependent Role="tickets">
        <PropertyRef Name="customer_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_movie_attributes_movie">
    <End Role="movies" Type="Self.movy" Multiplicity="1" />
    <End Role="movie_attributes" Type="Self.movie_attributes" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="movie_attributes">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_movie_types_movie">
    <End Role="movies" Type="Self.movy" Multiplicity="1" />
    <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="movie_types">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_movie_types_type">
    <End Role="types" Type="Self.type" Multiplicity="1" />
    <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="types">
        <PropertyRef Name="type_id" />
      </Principal>
      <Dependent Role="movie_types">
        <PropertyRef Name="type_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_prices_movie">
    <End Role="movies" Type="Self.movy" Multiplicity="1" />
    <End Role="prices" Type="Self.price" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="prices">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_schedules_movie">
    <End Role="movies" Type="Self.movy" Multiplicity="1" />
    <End Role="schedules" Type="Self.schedule" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="schedules">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_room_sections_room">
    <End Role="rooms" Type="Self.room" Multiplicity="1" />
    <End Role="room_sections" Type="Self.room_sections" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="rooms">
        <PropertyRef Name="room_id" />
      </Principal>
      <Dependent Role="room_sections">
        <PropertyRef Name="room_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_schedules_room">
    <End Role="rooms" Type="Self.room" Multiplicity="1" />
    <End Role="schedules" Type="Self.schedule" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="rooms">
        <PropertyRef Name="room_id" />
      </Principal>
      <Dependent Role="schedules">
        <PropertyRef Name="room_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_tickets_schedules">
    <End Role="schedules" Type="Self.schedule" Multiplicity="1" />
    <End Role="tickets" Type="Self.ticket" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="schedules">
        <PropertyRef Name="schedule_id" />
      </Principal>
      <Dependent Role="tickets">
        <PropertyRef Name="schedule_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_tickets_staff">
    <End Role="users" Type="Self.user" Multiplicity="1" />
    <End Role="tickets" Type="Self.ticket" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="users">
        <PropertyRef Name="user_id" />
      </Principal>
      <Dependent Role="tickets">
        <PropertyRef Name="staff_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityContainer Name="demo_wfsummerEntities" annotation:LazyLoadingEnabled="true">
    <EntitySet Name="customers" EntityType="Self.customer" />
    <EntitySet Name="movie_attributes" EntityType="Self.movie_attributes" />
    <EntitySet Name="movie_types" EntityType="Self.movie_types" />
    <EntitySet Name="movies" EntityType="Self.movy" />
    <EntitySet Name="prices" EntityType="Self.price" />
    <EntitySet Name="room_sections" EntityType="Self.room_sections" />
    <EntitySet Name="rooms" EntityType="Self.room" />
    <EntitySet Name="schedules" EntityType="Self.schedule" />
    <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagram" />
    <EntitySet Name="tickets" EntityType="Self.ticket" />
    <EntitySet Name="types" EntityType="Self.type" />
    <EntitySet Name="users" EntityType="Self.user" />
    <AssociationSet Name="FK_tickets_customer" Association="Self.FK_tickets_customer">
      <End Role="customers" EntitySet="customers" />
      <End Role="tickets" EntitySet="tickets" />
    </AssociationSet>
    <AssociationSet Name="FK_movie_attributes_movie" Association="Self.FK_movie_attributes_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="movie_attributes" EntitySet="movie_attributes" />
    </AssociationSet>
    <AssociationSet Name="FK_movie_types_movie" Association="Self.FK_movie_types_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="movie_types" EntitySet="movie_types" />
    </AssociationSet>
    <AssociationSet Name="FK_movie_types_type" Association="Self.FK_movie_types_type">
      <End Role="types" EntitySet="types" />
      <End Role="movie_types" EntitySet="movie_types" />
    </AssociationSet>
    <AssociationSet Name="FK_prices_movie" Association="Self.FK_prices_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="prices" EntitySet="prices" />
    </AssociationSet>
    <AssociationSet Name="FK_schedules_movie" Association="Self.FK_schedules_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="schedules" EntitySet="schedules" />
    </AssociationSet>
    <AssociationSet Name="FK_room_sections_room" Association="Self.FK_room_sections_room">
      <End Role="rooms" EntitySet="rooms" />
      <End Role="room_sections" EntitySet="room_sections" />
    </AssociationSet>
    <AssociationSet Name="FK_schedules_room" Association="Self.FK_schedules_room">
      <End Role="rooms" EntitySet="rooms" />
      <End Role="schedules" EntitySet="schedules" />
    </AssociationSet>
    <AssociationSet Name="FK_tickets_schedules" Association="Self.FK_tickets_schedules">
      <End Role="schedules" EntitySet="schedules" />
      <End Role="tickets" EntitySet="tickets" />
    </AssociationSet>
    <AssociationSet Name="FK_tickets_staff" Association="Self.FK_tickets_staff">
      <End Role="users" EntitySet="users" />
      <End Role="tickets" EntitySet="tickets" />
    </AssociationSet>
    <FunctionImport Name="sp_alterdiagram">
      <Parameter Name="diagramname" Mode="In" Type="String" />
      <Parameter Name="owner_id" Mode="In" Type="Int32" />
      <Parameter Name="version" Mode="In" Type="Int32" />
      <Parameter Name="definition" Mode="In" Type="Binary" />
    </FunctionImport>
    <FunctionImport Name="sp_creatediagram">
      <Parameter Name="diagramname" Mode="In" Type="String" />
      <Parameter Name="owner_id" Mode="In" Type="Int32" />
      <Parameter Name="version" Mode="In" Type="Int32" />
      <Parameter Name="definition" Mode="In" Type="Binary" />
    </FunctionImport>
    <FunctionImport Name="sp_dropdiagram">
      <Parameter Name="diagramname" Mode="In" Type="String" />
      <Parameter Name="owner_id" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="sp_helpdiagramdefinition" ReturnType="Collection(demo_wfsummerModel.sp_helpdiagramdefinition_Result)">
      <Parameter Name="diagramname" Mode="In" Type="String" />
      <Parameter Name="owner_id" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="sp_helpdiagrams" ReturnType="Collection(demo_wfsummerModel.sp_helpdiagrams_Result)">
      <Parameter Name="diagramname" Mode="In" Type="String" />
      <Parameter Name="owner_id" Mode="In" Type="Int32" />
    </FunctionImport>
    <FunctionImport Name="sp_renamediagram">
      <Parameter Name="diagramname" Mode="In" Type="String" />
      <Parameter Name="owner_id" Mode="In" Type="Int32" />
      <Parameter Name="new_diagramname" Mode="In" Type="String" />
    </FunctionImport>
    <FunctionImport Name="sp_upgraddiagrams" />
  </EntityContainer>
  <ComplexType Name="sp_helpdiagramdefinition_Result">
    <Property Type="Int32" Name="version" Nullable="true" />
    <Property Type="Binary" Name="definition" Nullable="true" />
  </ComplexType>
  <ComplexType Name="sp_helpdiagrams_Result">
    <Property Type="String" Name="Database" Nullable="true" MaxLength="128" />
    <Property Type="String" Name="Name" Nullable="false" MaxLength="128" />
    <Property Type="Int32" Name="ID" Nullable="false" />
    <Property Type="String" Name="Owner" Nullable="true" MaxLength="128" />
    <Property Type="Int32" Name="OwnerID" Nullable="false" />
  </ComplexType>
</Schema>