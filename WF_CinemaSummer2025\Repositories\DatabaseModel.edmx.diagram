<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="f77c7c1ec1a645b98f84195bc6844ce7" Name="Diagram1">
        <EntityTypeShape EntityType="demo_wfsummerModel.customer" Width="1.5" PointX="6" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.movie_attributes" Width="1.5" PointX="3" PointY="6.75" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.movie_types" Width="1.5" PointX="6" PointY="8.375" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.movy" Width="1.5" PointX="0.75" PointY="7.5" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.price" Width="1.5" PointX="3" PointY="12.625" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.room_sections" Width="1.5" PointX="6" PointY="16" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.room" Width="1.5" PointX="3.75" PointY="3.125" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.schedule" Width="1.5" PointX="6" PointY="4.125" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.sysdiagram" Width="1.5" PointX="0.75" PointY="2.375" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.ticket" Width="1.5" PointX="8.25" PointY="3.625" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.type" Width="1.5" PointX="3.75" PointY="17.5" IsExpanded="true" />
        <EntityTypeShape EntityType="demo_wfsummerModel.user" Width="1.5" PointX="6" PointY="12.625" IsExpanded="true" />
        <AssociationConnector Association="demo_wfsummerModel.FK_tickets_customer" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_movie_attributes_movie" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_movie_types_movie" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_movie_types_type" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_prices_movie" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_schedules_movie" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_room_sections_room" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_schedules_room" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_tickets_schedules" ManuallyRouted="false" />
        <AssociationConnector Association="demo_wfsummerModel.FK_tickets_staff" ManuallyRouted="false" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>