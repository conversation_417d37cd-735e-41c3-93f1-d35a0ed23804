using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WF_CinemaSummer2025.Repositories;
using WF_CinemaSummer2025.Services;
using WF_CinemaSummer2025.UIs.Form_Helper;

namespace WF_CinemaSummer2025.UIs.Staff
{
    public partial class StaffManagementForm : Form
    {
        private UserService userService;
        private SmoothLoadingPanel loadingPanel;
        private List<user> staffList;

        public StaffManagementForm()
        {
            InitializeComponent();
            userService = new UserService();
            SetupForm();
            SetupLoadingPanel();
        }

        private void SetupForm()
        {
            this.Text = "Quản lý nhân viên";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void SetupLoadingPanel()
        {
            loadingPanel = new SmoothLoadingPanel
            {
                Size = new Size(250, 140),
                Visible = false
            };
            this.Controls.Add(loadingPanel);
            CenterLoadingPanel();
        }

        private void CenterLoadingPanel()
        {
            loadingPanel.Location = new Point(
                (this.Width - loadingPanel.Width) / 2,
                (this.Height - loadingPanel.Height) / 2
            );
        }

        private async void StaffManagementForm_Load(object sender, EventArgs e)
        {
            await LoadStaffData();
        }

        private async Task LoadStaffData()
        {
            try
            {
                loadingPanel.StartLoading("Đang tải danh sách nhân viên...");
                CenterLoadingPanel();

                // Simulate loading delay
                await Task.Delay(1000);

                staffList = userService.GetAllUsers().Where(u => u.isdeleted != true).ToList();
                
                // Clear existing data
                dgvStaff.Rows.Clear();

                // Load data to DataGridView
                foreach (var staff in staffList)
                {
                    string roleName = GetRoleName(staff.roleid);
                    string status = staff.isdeleted == true ? "Đã xóa" : "Hoạt động";
                    
                    dgvStaff.Rows.Add(
                        staff.user_id,
                        staff.user_name,
                        staff.cccd,
                        roleName,
                        status,
                        staff.created_at?.ToString("dd/MM/yyyy") ?? ""
                    );
                }

                lblTotalStaff.Text = $"Tổng số nhân viên: {staffList.Count}";
                loadingPanel.LoadingText = "Tải hoàn thành!";
                await Task.Delay(500);
            }
            catch (Exception ex)
            {
                messageForm.ShowError($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi");
            }
            finally
            {
                loadingPanel.StopLoading();
            }
        }

        private string GetRoleName(int roleId)
        {
            switch (roleId)
            {
                case 0: return "Quản trị viên";
                case 1: return "Nhân viên";
                case 2: return "Thu ngân";
                default: return "Không xác định";
            }
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new AddEditStaffForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                await LoadStaffData();
                messageForm.ShowSuccess("Thêm nhân viên thành công!");
            }
        }

        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvStaff.SelectedRows.Count == 0)
            {
                messageForm.ShowWarning("Vui lòng chọn nhân viên cần sửa!");
                return;
            }

            string userId = dgvStaff.SelectedRows[0].Cells["UserId"].Value.ToString();
            var staff = staffList.FirstOrDefault(s => s.user_id == userId);
            
            if (staff != null)
            {
                var editForm = new AddEditStaffForm(staff);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadStaffData();
                    messageForm.ShowSuccess("Cập nhật nhân viên thành công!");
                }
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvStaff.SelectedRows.Count == 0)
            {
                messageForm.ShowWarning("Vui lòng chọn nhân viên cần xóa!");
                return;
            }

            string userId = dgvStaff.SelectedRows[0].Cells["UserId"].Value.ToString();
            string userName = dgvStaff.SelectedRows[0].Cells["UserName"].Value.ToString();

            var result = messageForm.ShowQuestion(
                $"Bạn có chắc chắn muốn xóa nhân viên '{userName}'?\n\nHành động này không thể hoàn tác!",
                "Xác nhận xóa"
            );

            if (result == messageForm.MessageResult.Yes)
            {
                try
                {
                    loadingPanel.StartLoading("Đang xóa nhân viên...");
                    CenterLoadingPanel();

                    await Task.Delay(1000); // Simulate processing

                    bool success = userService.DeleteUser(userId);
                    
                    if (success)
                    {
                        await LoadStaffData();
                        messageForm.ShowSuccess("Xóa nhân viên thành công!");
                    }
                    else
                    {
                        messageForm.ShowError("Không thể xóa nhân viên!");
                    }
                }
                catch (Exception ex)
                {
                    messageForm.ShowError($"Lỗi khi xóa nhân viên: {ex.Message}");
                }
                finally
                {
                    loadingPanel.StopLoading();
                }
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadStaffData();
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            SearchStaff();
        }

        private void txtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                SearchStaff();
            }
        }

        private void SearchStaff()
        {
            string searchText = txtSearch.Text.Trim().ToLower();
            
            if (string.IsNullOrEmpty(searchText))
            {
                // Show all staff
                LoadStaffToGrid(staffList);
                return;
            }

            var filteredStaff = staffList.Where(s => 
                s.user_id.ToLower().Contains(searchText) ||
                s.user_name.ToLower().Contains(searchText) ||
                (s.cccd != null && s.cccd.ToLower().Contains(searchText))
            ).ToList();

            LoadStaffToGrid(filteredStaff);
            lblTotalStaff.Text = $"Tìm thấy: {filteredStaff.Count} nhân viên";
        }

        private void LoadStaffToGrid(List<user> staffs)
        {
            dgvStaff.Rows.Clear();
            
            foreach (var staff in staffs)
            {
                string roleName = GetRoleName(staff.roleid);
                string status = staff.isdeleted == true ? "Đã xóa" : "Hoạt động";
                
                dgvStaff.Rows.Add(
                    staff.user_id,
                    staff.user_name,
                    staff.cccd,
                    roleName,
                    status,
                    staff.created_at?.ToString("dd/MM/yyyy") ?? ""
                );
            }
        }

        private void dgvStaff_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            CenterLoadingPanel();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            userService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
