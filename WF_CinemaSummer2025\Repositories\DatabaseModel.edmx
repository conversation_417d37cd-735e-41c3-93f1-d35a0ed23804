﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="demo_wfsummerModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="customers">
          <Key>
            <PropertyRef Name="customer_id" />
          </Key>
          <Property Name="customer_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="fullname" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="phone_number" Type="nvarchar" MaxLength="15" />
          <Property Name="dob" Type="date" />
          <Property Name="created_at" Type="datetime" />
        </EntityType>
        <EntityType Name="movie_attributes">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="attribute_name" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="attribute_value" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="created_at" Type="datetime" />
        </EntityType>
        <EntityType Name="movie_types">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="type_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="created_at" Type="datetime" />
        </EntityType>
        <EntityType Name="movies">
          <Key>
            <PropertyRef Name="movie_id" />
          </Key>
          <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="movie_name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="thumbnail_url" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="thumbnail_public_id" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="video_trailer_url" Type="nvarchar" MaxLength="255" Nullable="false" />
          <Property Name="video_public_id" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="created_at" Type="datetime" />
          <Property Name="updated_at" Type="datetime" />
          <Property Name="rate" Type="decimal" Precision="3" Scale="1" />
          <Property Name="duration" Type="int" Nullable="false" />
          <Property Name="is_deleted" Type="bit" />
        </EntityType>
        <EntityType Name="prices">
          <Key>
            <PropertyRef Name="price_id" />
          </Key>
          <Property Name="price_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="is_vip" Type="bit" Nullable="false" />
          <Property Name="price_value" Type="decimal" Precision="10" Scale="2" Nullable="false" />
          <Property Name="ticket_type_id" Type="int" Nullable="false" />
          <Property Name="created_at" Type="datetime" />
          <Property Name="updated_at" Type="datetime" />
        </EntityType>
        <EntityType Name="room_sections">
          <Key>
            <PropertyRef Name="section_id" />
          </Key>
          <Property Name="section_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="section_name" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="room_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="num_rows" Type="int" Nullable="false" />
          <Property Name="seat_per_row" Type="int" Nullable="false" />
          <Property Name="row_num_vip" Type="int" />
          <Property Name="start_row_num_vip" Type="int" />
          <Property Name="created_at" Type="datetime" />
          <Property Name="updated_at" Type="datetime" />
        </EntityType>
        <EntityType Name="rooms">
          <Key>
            <PropertyRef Name="room_id" />
          </Key>
          <Property Name="room_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="room_name" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="description" Type="nvarchar" MaxLength="500" />
          <Property Name="status" Type="nvarchar" MaxLength="20" Nullable="false" />
          <Property Name="capacity" Type="int" Nullable="false" />
          <Property Name="created_at" Type="datetime" />
          <Property Name="updated_at" Type="datetime" />
        </EntityType>
        <EntityType Name="schedules">
          <Key>
            <PropertyRef Name="schedule_id" />
          </Key>
          <Property Name="schedule_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="room_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="show_time" Type="datetime2" Precision="7" Nullable="false" />
          <Property Name="created_at" Type="datetime2" Precision="7" />
          <Property Name="updated_at" Type="datetime2" Precision="7" />
        </EntityType>
        <EntityType Name="sysdiagrams">
          <Key>
            <PropertyRef Name="diagram_id" />
          </Key>
          <Property Name="name" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="principal_id" Type="int" Nullable="false" />
          <Property Name="diagram_id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="version" Type="int" />
          <Property Name="definition" Type="varbinary(max)" />
        </EntityType>
        <EntityType Name="tickets">
          <Key>
            <PropertyRef Name="ticket_id" />
          </Key>
          <Property Name="ticket_id" Type="char" MaxLength="12" Nullable="false" />
          <Property Name="customer_id" Type="uniqueidentifier" />
          <Property Name="staff_id" Type="char" MaxLength="8" Nullable="false" />
          <Property Name="ticket_type_id" Type="int" Nullable="false" />
          <Property Name="seat_label" Type="nvarchar" MaxLength="10" Nullable="false" />
          <Property Name="price" Type="decimal" Precision="10" Scale="2" Nullable="false" />
          <Property Name="created_at" Type="datetime" />
          <Property Name="updated_at" Type="datetime" />
          <Property Name="payment" Type="int" Nullable="false" />
          <Property Name="ispaid" Type="bit" />
          <Property Name="schedule_id" Type="uniqueidentifier" Nullable="false" />
        </EntityType>
        <EntityType Name="types">
          <Key>
            <PropertyRef Name="type_id" />
          </Key>
          <Property Name="type_id" Type="uniqueidentifier" Nullable="false" />
          <Property Name="type_name" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="created_at" Type="datetime" />
          <Property Name="updated_at" Type="datetime" />
        </EntityType>
        <EntityType Name="users">
          <Key>
            <PropertyRef Name="user_id" />
          </Key>
          <Property Name="user_id" Type="char" MaxLength="8" Nullable="false" />
          <Property Name="user_name" Type="nvarchar" MaxLength="50" Nullable="false" />
          <Property Name="password" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="cccd" Type="nvarchar" MaxLength="20" />
          <Property Name="roleid" Type="int" Nullable="false" />
          <Property Name="isdeleted" Type="bit" />
          <Property Name="created_at" Type="datetime" />
        </EntityType>
        <Association Name="FK_movie_attributes_movie">
          <End Role="movies" Type="Self.movies" Multiplicity="1" />
          <End Role="movie_attributes" Type="Self.movie_attributes" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="movie_attributes">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_movie_types_movie">
          <End Role="movies" Type="Self.movies" Multiplicity="1" />
          <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="movie_types">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_movie_types_type">
          <End Role="types" Type="Self.types" Multiplicity="1" />
          <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="types">
              <PropertyRef Name="type_id" />
            </Principal>
            <Dependent Role="movie_types">
              <PropertyRef Name="type_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_prices_movie">
          <End Role="movies" Type="Self.movies" Multiplicity="1" />
          <End Role="prices" Type="Self.prices" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="prices">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_room_sections_room">
          <End Role="rooms" Type="Self.rooms" Multiplicity="1" />
          <End Role="room_sections" Type="Self.room_sections" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="rooms">
              <PropertyRef Name="room_id" />
            </Principal>
            <Dependent Role="room_sections">
              <PropertyRef Name="room_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_schedules_movie">
          <End Role="movies" Type="Self.movies" Multiplicity="1" />
          <End Role="schedules" Type="Self.schedules" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="schedules">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_schedules_room">
          <End Role="rooms" Type="Self.rooms" Multiplicity="1" />
          <End Role="schedules" Type="Self.schedules" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="rooms">
              <PropertyRef Name="room_id" />
            </Principal>
            <Dependent Role="schedules">
              <PropertyRef Name="room_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tickets_customer">
          <End Role="customers" Type="Self.customers" Multiplicity="0..1" />
          <End Role="tickets" Type="Self.tickets" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="customers">
              <PropertyRef Name="customer_id" />
            </Principal>
            <Dependent Role="tickets">
              <PropertyRef Name="customer_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tickets_schedules">
          <End Role="schedules" Type="Self.schedules" Multiplicity="1" />
          <End Role="tickets" Type="Self.tickets" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="schedules">
              <PropertyRef Name="schedule_id" />
            </Principal>
            <Dependent Role="tickets">
              <PropertyRef Name="schedule_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tickets_staff">
          <End Role="users" Type="Self.users" Multiplicity="1" />
          <End Role="tickets" Type="Self.tickets" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="users">
              <PropertyRef Name="user_id" />
            </Principal>
            <Dependent Role="tickets">
              <PropertyRef Name="staff_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Function Name="fn_diagramobjects" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="true" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" ReturnType="int" />
        <Function Name="sp_alterdiagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
          <Parameter Name="owner_id" Type="int" Mode="In" />
          <Parameter Name="version" Type="int" Mode="In" />
          <Parameter Name="definition" Type="varbinary(max)" Mode="In" />
        </Function>
        <Function Name="sp_creatediagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
          <Parameter Name="owner_id" Type="int" Mode="In" />
          <Parameter Name="version" Type="int" Mode="In" />
          <Parameter Name="definition" Type="varbinary(max)" Mode="In" />
        </Function>
        <Function Name="sp_dropdiagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
          <Parameter Name="owner_id" Type="int" Mode="In" />
        </Function>
        <Function Name="sp_helpdiagramdefinition" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
          <Parameter Name="owner_id" Type="int" Mode="In" />
        </Function>
        <Function Name="sp_helpdiagrams" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
          <Parameter Name="owner_id" Type="int" Mode="In" />
        </Function>
        <Function Name="sp_renamediagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
          <Parameter Name="owner_id" Type="int" Mode="In" />
          <Parameter Name="new_diagramname" Type="nvarchar" Mode="In" />
        </Function>
        <Function Name="sp_upgraddiagrams" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
        <EntityContainer Name="demo_wfsummerModelStoreContainer">
          <EntitySet Name="customers" EntityType="Self.customers" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="movie_attributes" EntityType="Self.movie_attributes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="movie_types" EntityType="Self.movie_types" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="movies" EntityType="Self.movies" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="prices" EntityType="Self.prices" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="room_sections" EntityType="Self.room_sections" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="rooms" EntityType="Self.rooms" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="schedules" EntityType="Self.schedules" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagrams" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="tickets" EntityType="Self.tickets" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="types" EntityType="Self.types" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="users" EntityType="Self.users" Schema="dbo" store:Type="Tables" />
          <AssociationSet Name="FK_movie_attributes_movie" Association="Self.FK_movie_attributes_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="movie_attributes" EntitySet="movie_attributes" />
          </AssociationSet>
          <AssociationSet Name="FK_movie_types_movie" Association="Self.FK_movie_types_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="movie_types" EntitySet="movie_types" />
          </AssociationSet>
          <AssociationSet Name="FK_movie_types_type" Association="Self.FK_movie_types_type">
            <End Role="types" EntitySet="types" />
            <End Role="movie_types" EntitySet="movie_types" />
          </AssociationSet>
          <AssociationSet Name="FK_prices_movie" Association="Self.FK_prices_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="prices" EntitySet="prices" />
          </AssociationSet>
          <AssociationSet Name="FK_room_sections_room" Association="Self.FK_room_sections_room">
            <End Role="rooms" EntitySet="rooms" />
            <End Role="room_sections" EntitySet="room_sections" />
          </AssociationSet>
          <AssociationSet Name="FK_schedules_movie" Association="Self.FK_schedules_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="schedules" EntitySet="schedules" />
          </AssociationSet>
          <AssociationSet Name="FK_schedules_room" Association="Self.FK_schedules_room">
            <End Role="rooms" EntitySet="rooms" />
            <End Role="schedules" EntitySet="schedules" />
          </AssociationSet>
          <AssociationSet Name="FK_tickets_customer" Association="Self.FK_tickets_customer">
            <End Role="customers" EntitySet="customers" />
            <End Role="tickets" EntitySet="tickets" />
          </AssociationSet>
          <AssociationSet Name="FK_tickets_schedules" Association="Self.FK_tickets_schedules">
            <End Role="schedules" EntitySet="schedules" />
            <End Role="tickets" EntitySet="tickets" />
          </AssociationSet>
          <AssociationSet Name="FK_tickets_staff" Association="Self.FK_tickets_staff">
            <End Role="users" EntitySet="users" />
            <End Role="tickets" EntitySet="tickets" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="demo_wfsummerModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="customer">
          <Key>
            <PropertyRef Name="customer_id" />
          </Key>
          <Property Name="customer_id" Type="Guid" Nullable="false" />
          <Property Name="fullname" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="phone_number" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="dob" Type="DateTime" Precision="0" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tickets" Relationship="Self.FK_tickets_customer" FromRole="customers" ToRole="tickets" />
        </EntityType>
        <EntityType Name="movie_attributes">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="Guid" Nullable="false" />
          <Property Name="attribute_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="attribute_value" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="movie_id" Type="Guid" Nullable="false" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="movy" Relationship="Self.FK_movie_attributes_movie" FromRole="movie_attributes" ToRole="movies" />
        </EntityType>
        <EntityType Name="movie_types">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="Guid" Nullable="false" />
          <Property Name="movie_id" Type="Guid" Nullable="false" />
          <Property Name="type_id" Type="Guid" Nullable="false" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="movy" Relationship="Self.FK_movie_types_movie" FromRole="movie_types" ToRole="movies" />
          <NavigationProperty Name="type" Relationship="Self.FK_movie_types_type" FromRole="movie_types" ToRole="types" />
        </EntityType>
        <EntityType Name="movy">
          <Key>
            <PropertyRef Name="movie_id" />
          </Key>
          <Property Name="movie_id" Type="Guid" Nullable="false" />
          <Property Name="movie_name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="thumbnail_url" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="thumbnail_public_id" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="video_trailer_url" Type="String" MaxLength="255" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="video_public_id" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <Property Name="updated_at" Type="DateTime" Precision="3" />
          <Property Name="rate" Type="Decimal" Precision="3" Scale="1" />
          <Property Name="duration" Type="Int32" Nullable="false" />
          <Property Name="is_deleted" Type="Boolean" />
          <NavigationProperty Name="movie_attributes" Relationship="Self.FK_movie_attributes_movie" FromRole="movies" ToRole="movie_attributes" />
          <NavigationProperty Name="movie_types" Relationship="Self.FK_movie_types_movie" FromRole="movies" ToRole="movie_types" />
          <NavigationProperty Name="prices" Relationship="Self.FK_prices_movie" FromRole="movies" ToRole="prices" />
          <NavigationProperty Name="schedules" Relationship="Self.FK_schedules_movie" FromRole="movies" ToRole="schedules" />
        </EntityType>
        <EntityType Name="price">
          <Key>
            <PropertyRef Name="price_id" />
          </Key>
          <Property Name="price_id" Type="Guid" Nullable="false" />
          <Property Name="movie_id" Type="Guid" Nullable="false" />
          <Property Name="is_vip" Type="Boolean" Nullable="false" />
          <Property Name="price_value" Type="Decimal" Precision="10" Scale="2" Nullable="false" />
          <Property Name="ticket_type_id" Type="Int32" Nullable="false" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <Property Name="updated_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="movy" Relationship="Self.FK_prices_movie" FromRole="prices" ToRole="movies" />
        </EntityType>
        <EntityType Name="room_sections">
          <Key>
            <PropertyRef Name="section_id" />
          </Key>
          <Property Name="section_id" Type="Guid" Nullable="false" />
          <Property Name="section_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="room_id" Type="Guid" Nullable="false" />
          <Property Name="num_rows" Type="Int32" Nullable="false" />
          <Property Name="seat_per_row" Type="Int32" Nullable="false" />
          <Property Name="row_num_vip" Type="Int32" />
          <Property Name="start_row_num_vip" Type="Int32" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <Property Name="updated_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="room" Relationship="Self.FK_room_sections_room" FromRole="room_sections" ToRole="rooms" />
        </EntityType>
        <EntityType Name="room">
          <Key>
            <PropertyRef Name="room_id" />
          </Key>
          <Property Name="room_id" Type="Guid" Nullable="false" />
          <Property Name="room_name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="description" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="status" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="capacity" Type="Int32" Nullable="false" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <Property Name="updated_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="room_sections" Relationship="Self.FK_room_sections_room" FromRole="rooms" ToRole="room_sections" />
          <NavigationProperty Name="schedules" Relationship="Self.FK_schedules_room" FromRole="rooms" ToRole="schedules" />
        </EntityType>
        <EntityType Name="schedule">
          <Key>
            <PropertyRef Name="schedule_id" />
          </Key>
          <Property Name="schedule_id" Type="Guid" Nullable="false" />
          <Property Name="room_id" Type="Guid" Nullable="false" />
          <Property Name="movie_id" Type="Guid" Nullable="false" />
          <Property Name="show_time" Type="DateTime" Nullable="false" Precision="7" />
          <Property Name="created_at" Type="DateTime" Precision="7" />
          <Property Name="updated_at" Type="DateTime" Precision="7" />
          <NavigationProperty Name="movy" Relationship="Self.FK_schedules_movie" FromRole="schedules" ToRole="movies" />
          <NavigationProperty Name="room" Relationship="Self.FK_schedules_room" FromRole="schedules" ToRole="rooms" />
          <NavigationProperty Name="tickets" Relationship="Self.FK_tickets_schedules" FromRole="schedules" ToRole="tickets" />
        </EntityType>
        <EntityType Name="sysdiagram">
          <Key>
            <PropertyRef Name="diagram_id" />
          </Key>
          <Property Name="name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="principal_id" Type="Int32" Nullable="false" />
          <Property Name="diagram_id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="version" Type="Int32" />
          <Property Name="definition" Type="Binary" MaxLength="Max" FixedLength="false" />
        </EntityType>
        <EntityType Name="ticket">
          <Key>
            <PropertyRef Name="ticket_id" />
          </Key>
          <Property Name="ticket_id" Type="String" MaxLength="12" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="customer_id" Type="Guid" />
          <Property Name="staff_id" Type="String" MaxLength="8" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="ticket_type_id" Type="Int32" Nullable="false" />
          <Property Name="seat_label" Type="String" MaxLength="10" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="price" Type="Decimal" Precision="10" Scale="2" Nullable="false" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <Property Name="updated_at" Type="DateTime" Precision="3" />
          <Property Name="payment" Type="Int32" Nullable="false" />
          <Property Name="ispaid" Type="Boolean" />
          <Property Name="schedule_id" Type="Guid" Nullable="false" />
          <NavigationProperty Name="customer" Relationship="Self.FK_tickets_customer" FromRole="tickets" ToRole="customers" />
          <NavigationProperty Name="schedule" Relationship="Self.FK_tickets_schedules" FromRole="tickets" ToRole="schedules" />
          <NavigationProperty Name="user" Relationship="Self.FK_tickets_staff" FromRole="tickets" ToRole="users" />
        </EntityType>
        <EntityType Name="type">
          <Key>
            <PropertyRef Name="type_id" />
          </Key>
          <Property Name="type_id" Type="Guid" Nullable="false" />
          <Property Name="type_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <Property Name="updated_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="movie_types" Relationship="Self.FK_movie_types_type" FromRole="types" ToRole="movie_types" />
        </EntityType>
        <EntityType Name="user">
          <Key>
            <PropertyRef Name="user_id" />
          </Key>
          <Property Name="user_id" Type="String" MaxLength="8" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="user_name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="password" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="cccd" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="roleid" Type="Int32" Nullable="false" />
          <Property Name="isdeleted" Type="Boolean" />
          <Property Name="created_at" Type="DateTime" Precision="3" />
          <NavigationProperty Name="tickets" Relationship="Self.FK_tickets_staff" FromRole="users" ToRole="tickets" />
        </EntityType>
        <Association Name="FK_tickets_customer">
          <End Role="customers" Type="Self.customer" Multiplicity="0..1" />
          <End Role="tickets" Type="Self.ticket" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="customers">
              <PropertyRef Name="customer_id" />
            </Principal>
            <Dependent Role="tickets">
              <PropertyRef Name="customer_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_movie_attributes_movie">
          <End Role="movies" Type="Self.movy" Multiplicity="1" />
          <End Role="movie_attributes" Type="Self.movie_attributes" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="movie_attributes">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_movie_types_movie">
          <End Role="movies" Type="Self.movy" Multiplicity="1" />
          <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="movie_types">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_movie_types_type">
          <End Role="types" Type="Self.type" Multiplicity="1" />
          <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="types">
              <PropertyRef Name="type_id" />
            </Principal>
            <Dependent Role="movie_types">
              <PropertyRef Name="type_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_prices_movie">
          <End Role="movies" Type="Self.movy" Multiplicity="1" />
          <End Role="prices" Type="Self.price" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="prices">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_schedules_movie">
          <End Role="movies" Type="Self.movy" Multiplicity="1" />
          <End Role="schedules" Type="Self.schedule" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="movies">
              <PropertyRef Name="movie_id" />
            </Principal>
            <Dependent Role="schedules">
              <PropertyRef Name="movie_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_room_sections_room">
          <End Role="rooms" Type="Self.room" Multiplicity="1" />
          <End Role="room_sections" Type="Self.room_sections" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="rooms">
              <PropertyRef Name="room_id" />
            </Principal>
            <Dependent Role="room_sections">
              <PropertyRef Name="room_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_schedules_room">
          <End Role="rooms" Type="Self.room" Multiplicity="1" />
          <End Role="schedules" Type="Self.schedule" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="rooms">
              <PropertyRef Name="room_id" />
            </Principal>
            <Dependent Role="schedules">
              <PropertyRef Name="room_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tickets_schedules">
          <End Role="schedules" Type="Self.schedule" Multiplicity="1" />
          <End Role="tickets" Type="Self.ticket" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="schedules">
              <PropertyRef Name="schedule_id" />
            </Principal>
            <Dependent Role="tickets">
              <PropertyRef Name="schedule_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_tickets_staff">
          <End Role="users" Type="Self.user" Multiplicity="1" />
          <End Role="tickets" Type="Self.ticket" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="users">
              <PropertyRef Name="user_id" />
            </Principal>
            <Dependent Role="tickets">
              <PropertyRef Name="staff_id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="demo_wfsummerEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="customers" EntityType="Self.customer" />
          <EntitySet Name="movie_attributes" EntityType="Self.movie_attributes" />
          <EntitySet Name="movie_types" EntityType="Self.movie_types" />
          <EntitySet Name="movies" EntityType="Self.movy" />
          <EntitySet Name="prices" EntityType="Self.price" />
          <EntitySet Name="room_sections" EntityType="Self.room_sections" />
          <EntitySet Name="rooms" EntityType="Self.room" />
          <EntitySet Name="schedules" EntityType="Self.schedule" />
          <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagram" />
          <EntitySet Name="tickets" EntityType="Self.ticket" />
          <EntitySet Name="types" EntityType="Self.type" />
          <EntitySet Name="users" EntityType="Self.user" />
          <AssociationSet Name="FK_tickets_customer" Association="Self.FK_tickets_customer">
            <End Role="customers" EntitySet="customers" />
            <End Role="tickets" EntitySet="tickets" />
          </AssociationSet>
          <AssociationSet Name="FK_movie_attributes_movie" Association="Self.FK_movie_attributes_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="movie_attributes" EntitySet="movie_attributes" />
          </AssociationSet>
          <AssociationSet Name="FK_movie_types_movie" Association="Self.FK_movie_types_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="movie_types" EntitySet="movie_types" />
          </AssociationSet>
          <AssociationSet Name="FK_movie_types_type" Association="Self.FK_movie_types_type">
            <End Role="types" EntitySet="types" />
            <End Role="movie_types" EntitySet="movie_types" />
          </AssociationSet>
          <AssociationSet Name="FK_prices_movie" Association="Self.FK_prices_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="prices" EntitySet="prices" />
          </AssociationSet>
          <AssociationSet Name="FK_schedules_movie" Association="Self.FK_schedules_movie">
            <End Role="movies" EntitySet="movies" />
            <End Role="schedules" EntitySet="schedules" />
          </AssociationSet>
          <AssociationSet Name="FK_room_sections_room" Association="Self.FK_room_sections_room">
            <End Role="rooms" EntitySet="rooms" />
            <End Role="room_sections" EntitySet="room_sections" />
          </AssociationSet>
          <AssociationSet Name="FK_schedules_room" Association="Self.FK_schedules_room">
            <End Role="rooms" EntitySet="rooms" />
            <End Role="schedules" EntitySet="schedules" />
          </AssociationSet>
          <AssociationSet Name="FK_tickets_schedules" Association="Self.FK_tickets_schedules">
            <End Role="schedules" EntitySet="schedules" />
            <End Role="tickets" EntitySet="tickets" />
          </AssociationSet>
          <AssociationSet Name="FK_tickets_staff" Association="Self.FK_tickets_staff">
            <End Role="users" EntitySet="users" />
            <End Role="tickets" EntitySet="tickets" />
          </AssociationSet>
          <FunctionImport Name="sp_alterdiagram">
            <Parameter Name="diagramname" Mode="In" Type="String" />
            <Parameter Name="owner_id" Mode="In" Type="Int32" />
            <Parameter Name="version" Mode="In" Type="Int32" />
            <Parameter Name="definition" Mode="In" Type="Binary" />
          </FunctionImport>
          <FunctionImport Name="sp_creatediagram">
            <Parameter Name="diagramname" Mode="In" Type="String" />
            <Parameter Name="owner_id" Mode="In" Type="Int32" />
            <Parameter Name="version" Mode="In" Type="Int32" />
            <Parameter Name="definition" Mode="In" Type="Binary" />
          </FunctionImport>
          <FunctionImport Name="sp_dropdiagram">
            <Parameter Name="diagramname" Mode="In" Type="String" />
            <Parameter Name="owner_id" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="sp_helpdiagramdefinition" ReturnType="Collection(demo_wfsummerModel.sp_helpdiagramdefinition_Result)">
            <Parameter Name="diagramname" Mode="In" Type="String" />
            <Parameter Name="owner_id" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="sp_helpdiagrams" ReturnType="Collection(demo_wfsummerModel.sp_helpdiagrams_Result)">
            <Parameter Name="diagramname" Mode="In" Type="String" />
            <Parameter Name="owner_id" Mode="In" Type="Int32" />
          </FunctionImport>
          <FunctionImport Name="sp_renamediagram">
            <Parameter Name="diagramname" Mode="In" Type="String" />
            <Parameter Name="owner_id" Mode="In" Type="Int32" />
            <Parameter Name="new_diagramname" Mode="In" Type="String" />
          </FunctionImport>
          <FunctionImport Name="sp_upgraddiagrams" />
        </EntityContainer>
        <ComplexType Name="sp_helpdiagramdefinition_Result">
          <Property Type="Int32" Name="version" Nullable="true" />
          <Property Type="Binary" Name="definition" Nullable="true" />
        </ComplexType>
        <ComplexType Name="sp_helpdiagrams_Result">
          <Property Type="String" Name="Database" Nullable="true" MaxLength="128" />
          <Property Type="String" Name="Name" Nullable="false" MaxLength="128" />
          <Property Type="Int32" Name="ID" Nullable="false" />
          <Property Type="String" Name="Owner" Nullable="true" MaxLength="128" />
          <Property Type="Int32" Name="OwnerID" Nullable="false" />
        </ComplexType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="demo_wfsummerModelStoreContainer" CdmEntityContainer="demo_wfsummerEntities">
          <EntitySetMapping Name="customers">
            <EntityTypeMapping TypeName="demo_wfsummerModel.customer">
              <MappingFragment StoreEntitySet="customers">
                <ScalarProperty Name="customer_id" ColumnName="customer_id" />
                <ScalarProperty Name="fullname" ColumnName="fullname" />
                <ScalarProperty Name="phone_number" ColumnName="phone_number" />
                <ScalarProperty Name="dob" ColumnName="dob" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="movie_attributes">
            <EntityTypeMapping TypeName="demo_wfsummerModel.movie_attributes">
              <MappingFragment StoreEntitySet="movie_attributes">
                <ScalarProperty Name="id" ColumnName="id" />
                <ScalarProperty Name="attribute_name" ColumnName="attribute_name" />
                <ScalarProperty Name="attribute_value" ColumnName="attribute_value" />
                <ScalarProperty Name="movie_id" ColumnName="movie_id" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="movie_types">
            <EntityTypeMapping TypeName="demo_wfsummerModel.movie_types">
              <MappingFragment StoreEntitySet="movie_types">
                <ScalarProperty Name="id" ColumnName="id" />
                <ScalarProperty Name="movie_id" ColumnName="movie_id" />
                <ScalarProperty Name="type_id" ColumnName="type_id" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="movies">
            <EntityTypeMapping TypeName="demo_wfsummerModel.movy">
              <MappingFragment StoreEntitySet="movies">
                <ScalarProperty Name="movie_id" ColumnName="movie_id" />
                <ScalarProperty Name="movie_name" ColumnName="movie_name" />
                <ScalarProperty Name="thumbnail_url" ColumnName="thumbnail_url" />
                <ScalarProperty Name="thumbnail_public_id" ColumnName="thumbnail_public_id" />
                <ScalarProperty Name="video_trailer_url" ColumnName="video_trailer_url" />
                <ScalarProperty Name="video_public_id" ColumnName="video_public_id" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
                <ScalarProperty Name="updated_at" ColumnName="updated_at" />
                <ScalarProperty Name="rate" ColumnName="rate" />
                <ScalarProperty Name="duration" ColumnName="duration" />
                <ScalarProperty Name="is_deleted" ColumnName="is_deleted" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="prices">
            <EntityTypeMapping TypeName="demo_wfsummerModel.price">
              <MappingFragment StoreEntitySet="prices">
                <ScalarProperty Name="price_id" ColumnName="price_id" />
                <ScalarProperty Name="movie_id" ColumnName="movie_id" />
                <ScalarProperty Name="is_vip" ColumnName="is_vip" />
                <ScalarProperty Name="price_value" ColumnName="price_value" />
                <ScalarProperty Name="ticket_type_id" ColumnName="ticket_type_id" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
                <ScalarProperty Name="updated_at" ColumnName="updated_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="room_sections">
            <EntityTypeMapping TypeName="demo_wfsummerModel.room_sections">
              <MappingFragment StoreEntitySet="room_sections">
                <ScalarProperty Name="section_id" ColumnName="section_id" />
                <ScalarProperty Name="section_name" ColumnName="section_name" />
                <ScalarProperty Name="room_id" ColumnName="room_id" />
                <ScalarProperty Name="num_rows" ColumnName="num_rows" />
                <ScalarProperty Name="seat_per_row" ColumnName="seat_per_row" />
                <ScalarProperty Name="row_num_vip" ColumnName="row_num_vip" />
                <ScalarProperty Name="start_row_num_vip" ColumnName="start_row_num_vip" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
                <ScalarProperty Name="updated_at" ColumnName="updated_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="rooms">
            <EntityTypeMapping TypeName="demo_wfsummerModel.room">
              <MappingFragment StoreEntitySet="rooms">
                <ScalarProperty Name="room_id" ColumnName="room_id" />
                <ScalarProperty Name="room_name" ColumnName="room_name" />
                <ScalarProperty Name="description" ColumnName="description" />
                <ScalarProperty Name="status" ColumnName="status" />
                <ScalarProperty Name="capacity" ColumnName="capacity" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
                <ScalarProperty Name="updated_at" ColumnName="updated_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="schedules">
            <EntityTypeMapping TypeName="demo_wfsummerModel.schedule">
              <MappingFragment StoreEntitySet="schedules">
                <ScalarProperty Name="schedule_id" ColumnName="schedule_id" />
                <ScalarProperty Name="room_id" ColumnName="room_id" />
                <ScalarProperty Name="movie_id" ColumnName="movie_id" />
                <ScalarProperty Name="show_time" ColumnName="show_time" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
                <ScalarProperty Name="updated_at" ColumnName="updated_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="sysdiagrams">
            <EntityTypeMapping TypeName="demo_wfsummerModel.sysdiagram">
              <MappingFragment StoreEntitySet="sysdiagrams">
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="principal_id" ColumnName="principal_id" />
                <ScalarProperty Name="diagram_id" ColumnName="diagram_id" />
                <ScalarProperty Name="version" ColumnName="version" />
                <ScalarProperty Name="definition" ColumnName="definition" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="tickets">
            <EntityTypeMapping TypeName="demo_wfsummerModel.ticket">
              <MappingFragment StoreEntitySet="tickets">
                <ScalarProperty Name="ticket_id" ColumnName="ticket_id" />
                <ScalarProperty Name="customer_id" ColumnName="customer_id" />
                <ScalarProperty Name="staff_id" ColumnName="staff_id" />
                <ScalarProperty Name="ticket_type_id" ColumnName="ticket_type_id" />
                <ScalarProperty Name="seat_label" ColumnName="seat_label" />
                <ScalarProperty Name="price" ColumnName="price" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
                <ScalarProperty Name="updated_at" ColumnName="updated_at" />
                <ScalarProperty Name="payment" ColumnName="payment" />
                <ScalarProperty Name="ispaid" ColumnName="ispaid" />
                <ScalarProperty Name="schedule_id" ColumnName="schedule_id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="types">
            <EntityTypeMapping TypeName="demo_wfsummerModel.type">
              <MappingFragment StoreEntitySet="types">
                <ScalarProperty Name="type_id" ColumnName="type_id" />
                <ScalarProperty Name="type_name" ColumnName="type_name" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
                <ScalarProperty Name="updated_at" ColumnName="updated_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="users">
            <EntityTypeMapping TypeName="demo_wfsummerModel.user">
              <MappingFragment StoreEntitySet="users">
                <ScalarProperty Name="user_id" ColumnName="user_id" />
                <ScalarProperty Name="user_name" ColumnName="user_name" />
                <ScalarProperty Name="password" ColumnName="password" />
                <ScalarProperty Name="cccd" ColumnName="cccd" />
                <ScalarProperty Name="roleid" ColumnName="roleid" />
                <ScalarProperty Name="isdeleted" ColumnName="isdeleted" />
                <ScalarProperty Name="created_at" ColumnName="created_at" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <FunctionImportMapping FunctionImportName="sp_alterdiagram" FunctionName="demo_wfsummerModel.Store.sp_alterdiagram" />
          <FunctionImportMapping FunctionImportName="sp_creatediagram" FunctionName="demo_wfsummerModel.Store.sp_creatediagram" />
          <FunctionImportMapping FunctionImportName="sp_dropdiagram" FunctionName="demo_wfsummerModel.Store.sp_dropdiagram" />
          <FunctionImportMapping FunctionImportName="sp_helpdiagramdefinition" FunctionName="demo_wfsummerModel.Store.sp_helpdiagramdefinition">
            <ResultMapping>
              <ComplexTypeMapping TypeName="demo_wfsummerModel.sp_helpdiagramdefinition_Result">
                <ScalarProperty Name="version" ColumnName="version" />
                <ScalarProperty Name="definition" ColumnName="definition" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="sp_helpdiagrams" FunctionName="demo_wfsummerModel.Store.sp_helpdiagrams">
            <ResultMapping>
              <ComplexTypeMapping TypeName="demo_wfsummerModel.sp_helpdiagrams_Result">
                <ScalarProperty Name="Database" ColumnName="Database" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="Owner" ColumnName="Owner" />
                <ScalarProperty Name="OwnerID" ColumnName="OwnerID" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <FunctionImportMapping FunctionImportName="sp_renamediagram" FunctionName="demo_wfsummerModel.Store.sp_renamediagram" />
          <FunctionImportMapping FunctionImportName="sp_upgraddiagrams" FunctionName="demo_wfsummerModel.Store.sp_upgraddiagrams" />
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>