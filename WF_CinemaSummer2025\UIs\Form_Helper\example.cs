﻿using System;
using System.Windows.Forms;
using WF_CinemaSummer2025.UIs.Form_Helper;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Ví dụ về cách sử dụng messageForm
    /// </summary>
    public class MessageFormExample
    {
        /// <summary>
        /// Ví dụ 1: S<PERSON> dụng phương thức static đơn giản
        /// </summary>
        public static void Example1()
        {
            var result = messageForm.ShowMessage("Bạn có muốn xóa dữ liệu này không?");

            if (result == messageForm.MessageResult.Yes)
            {
                MessageBox.Show("Người dùng chọn Có");
                // Thực hiện hành động xóa
            }
            else if (result == messageForm.MessageResult.No)
            {
                MessageBox.Show("Người dùng chọn Không");
                // Hủy bỏ hành động
            }
        }

        /// <summary>
        /// Ví dụ 2: Sử dụng với title tùy chỉnh
        /// </summary>
        public static void Example2()
        {
            var result = messageForm.ShowMessage(
                "Bạn có muốn lưu thay đổi trước khi thoát không?",
                "Xác nhận lưu"
            );

            switch (result)
            {
                case messageForm.MessageResult.Yes:
                    // Lưu dữ liệu
                    MessageBox.Show("Đã lưu thành công!");
                    break;
                case messageForm.MessageResult.No:
                    // Thoát mà không lưu
                    MessageBox.Show("Thoát mà không lưu");
                    break;
            }
        }

        /// <summary>
        /// Ví dụ 3: Sử dụng với parent form
        /// </summary>
        public static void Example3(Form parentForm)
        {
            var result = messageForm.ShowMessage(
                parentForm,
                "Bạn có chắc chắn muốn đăng xuất không?",
                "Xác nhận đăng xuất"
            );

            if (result == messageForm.MessageResult.Yes)
            {
                // Thực hiện đăng xuất
                MessageBox.Show("Đã đăng xuất!");
            }
        }

        /// <summary>
        /// Ví dụ 4: Sử dụng constructor trực tiếp để có nhiều control hơn
        /// </summary>
        public static void Example4()
        {
            using (var messageForm = new messageForm())
            {
                messageForm.SetMessage("Bạn có muốn tiếp tục thao tác này không?\n\nLưu ý: Hành động này không thể hoàn tác!");
                messageForm.Text = "Cảnh báo quan trọng";

                var dialogResult = messageForm.ShowDialog();

                if (messageForm.Result == messageForm.MessageResult.Yes)
                {
                    MessageBox.Show("Tiếp tục thực hiện...");
                }
                else
                {
                    MessageBox.Show("Đã hủy bỏ thao tác");
                }
            }
        }

        /// <summary>
        /// Ví dụ 5: Sử dụng trong event handler của button
        /// </summary>
        public static void DeleteButtonClick(object sender, EventArgs e)
        {
            var result = messageForm.ShowMessage(
                "Bạn có chắc chắn muốn xóa mục này không?\n\nDữ liệu sẽ bị mất vĩnh viễn!",
                "Xác nhận xóa"
            );

            if (result == messageForm.MessageResult.Yes)
            {
                try
                {
                    // Thực hiện logic xóa ở đây
                    // DeleteItem();
                    MessageBox.Show("Đã xóa thành công!");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi khi xóa: {ex.Message}");
                }
            }
        }
    }
}
