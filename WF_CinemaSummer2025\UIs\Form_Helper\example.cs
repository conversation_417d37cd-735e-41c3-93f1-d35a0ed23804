﻿using System;
using System.Windows.Forms;
using WF_CinemaSummer2025.UIs.Form_Helper;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Ví dụ về cách sử dụng messageForm với các loại message khác nhau
    /// </summary>
    public class MessageFormExample
    {
        /// <summary>
        /// Ví dụ 1: Hiển thị thông báo thành công
        /// </summary>
        public static void ShowSuccessExample()
        {
            var result = messageForm.ShowSuccess(
                "Dữ liệu đã được lưu thành công!\n\nTất cả thay đổi đã được cập nhật vào hệ thống.",
                "Lưu thành công"
            );

            // Với Success message, result sẽ luôn là OK
            if (result == messageForm.MessageResult.OK)
            {
                // C<PERSON> thể thực hiện hành động tiếp theo
                Console.WriteLine("Người dùng đã xác nhận thông báo thành công");
            }
        }

        /// <summary>
        /// Ví dụ 2: <PERSON><PERSON><PERSON> thị thông báo lỗi
        /// </summary>
        public static void ShowErrorExample()
        {
            var result = messageForm.ShowError(
                "Không thể kết nối đến cơ sở dữ liệu!\n\nVui lòng kiểm tra kết nối mạng và thử lại.",
                "Lỗi kết nối"
            );

            // Với Error message, result sẽ luôn là OK
            if (result == messageForm.MessageResult.OK)
            {
                // Có thể thực hiện hành động khắc phục
                Console.WriteLine("Người dùng đã xác nhận thông báo lỗi");
            }
        }

        /// <summary>
        /// Ví dụ 3: Hiển thị cảnh báo với lựa chọn
        /// </summary>
        public static void ShowWarningExample()
        {
            var result = messageForm.ShowWarning(
                "Bạn đang thực hiện thao tác có thể ảnh hưởng đến dữ liệu quan trọng.\n\nBạn có chắc chắn muốn tiếp tục?",
                "Cảnh báo quan trọng"
            );

            if (result == messageForm.MessageResult.Yes)
            {
                Console.WriteLine("Người dùng chọn tiếp tục thực hiện");
                // Thực hiện hành động
            }
            else if (result == messageForm.MessageResult.No)
            {
                Console.WriteLine("Người dùng chọn hủy bỏ");
                // Hủy bỏ hành động
            }
        }

        /// <summary>
        /// Ví dụ 4: Hiển thị câu hỏi xác nhận
        /// </summary>
        public static void ShowQuestionExample()
        {
            var result = messageForm.ShowQuestion(
                "Bạn có muốn xóa tất cả dữ liệu đã chọn không?\n\nHành động này không thể hoàn tác!",
                "Xác nhận xóa"
            );

            switch (result)
            {
                case messageForm.MessageResult.Yes:
                    Console.WriteLine("Thực hiện xóa dữ liệu");
                    // Thực hiện xóa
                    messageForm.ShowSuccess("Đã xóa dữ liệu thành công!");
                    break;
                case messageForm.MessageResult.No:
                    Console.WriteLine("Hủy bỏ thao tác xóa");
                    break;
            }
        }

        /// <summary>
        /// Ví dụ 5: Hiển thị thông tin
        /// </summary>
        public static void ShowInformationExample()
        {
            var result = messageForm.ShowInformation(
                "Hệ thống sẽ được bảo trì từ 2:00 AM đến 4:00 AM.\n\nVui lòng lưu công việc trước thời gian này.",
                "Thông báo bảo trì"
            );

            if (result == messageForm.MessageResult.OK)
            {
                Console.WriteLine("Người dùng đã đọc thông báo");
            }
        }

        /// <summary>
        /// Ví dụ 6: Sử dụng với parent form
        /// </summary>
        public static void ShowWithParentExample(Form parentForm)
        {
            // Hiển thị cảnh báo với parent form
            var result = messageForm.ShowWarning(
                parentForm,
                "Phiên làm việc sắp hết hạn.\n\nBạn có muốn gia hạn thêm 30 phút?",
                "Cảnh báo phiên làm việc"
            );

            if (result == messageForm.MessageResult.Yes)
            {
                messageForm.ShowSuccess(parentForm, "Đã gia hạn phiên làm việc thành công!");
            }
        }

        /// <summary>
        /// Ví dụ 7: Thay thế MessageBox.Show thông thường
        /// </summary>
        public static void ReplaceMessageBoxExample()
        {
            // Thay vì: MessageBox.Show("Thông báo", "Title", MessageBoxButtons.OK, MessageBoxIcon.Information);
            messageForm.ShowInformation("Thông báo", "Title");

            // Thay vì: MessageBox.Show("Lỗi", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            messageForm.ShowError("Lỗi", "Error");

            // Thay vì: MessageBox.Show("Cảnh báo", "Warning", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            var result = messageForm.ShowWarning("Cảnh báo", "Warning");

            // Thay vì: MessageBox.Show("Xác nhận", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            var confirmResult = messageForm.ShowQuestion("Xác nhận", "Confirm");
        }

        /// <summary>
        /// Ví dụ 8: Sử dụng trong try-catch
        /// </summary>
        public static void TryCatchExample()
        {
            try
            {
                // Thực hiện một số logic có thể gây lỗi
                // SomeRiskyOperation();

                messageForm.ShowSuccess("Thao tác đã hoàn thành thành công!");
            }
            catch (Exception ex)
            {
                messageForm.ShowError($"Đã xảy ra lỗi: {ex.Message}\n\nVui lòng thử lại sau.", "Lỗi hệ thống");
            }
        }
    }
}
