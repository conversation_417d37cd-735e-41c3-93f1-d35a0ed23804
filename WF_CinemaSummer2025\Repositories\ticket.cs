//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WF_CinemaSummer2025.Repositories
{
    using System;
    using System.Collections.Generic;
    
    public partial class ticket
    {
        public string ticket_id { get; set; }
        public Nullable<System.Guid> customer_id { get; set; }
        public string staff_id { get; set; }
        public int ticket_type_id { get; set; }
        public string seat_label { get; set; }
        public decimal price { get; set; }
        public Nullable<System.DateTime> created_at { get; set; }
        public Nullable<System.DateTime> updated_at { get; set; }
        public int payment { get; set; }
        public Nullable<bool> ispaid { get; set; }
        public System.Guid schedule_id { get; set; }
    
        public virtual customer customer { get; set; }
        public virtual schedule schedule { get; set; }
        public virtual user user { get; set; }
    }
}
