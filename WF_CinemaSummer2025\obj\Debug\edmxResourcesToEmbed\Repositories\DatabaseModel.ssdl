﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="demo_wfsummerModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
  <EntityType Name="customers">
    <Key>
      <PropertyRef Name="customer_id" />
    </Key>
    <Property Name="customer_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="fullname" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="phone_number" Type="nvarchar" MaxLength="15" />
    <Property Name="dob" Type="date" />
    <Property Name="created_at" Type="datetime" />
  </EntityType>
  <EntityType Name="movie_attributes">
    <Key>
      <PropertyRef Name="id" />
    </Key>
    <Property Name="id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="attribute_name" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="attribute_value" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="created_at" Type="datetime" />
  </EntityType>
  <EntityType Name="movie_types">
    <Key>
      <PropertyRef Name="id" />
    </Key>
    <Property Name="id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="type_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="created_at" Type="datetime" />
  </EntityType>
  <EntityType Name="movies">
    <Key>
      <PropertyRef Name="movie_id" />
    </Key>
    <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="movie_name" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="thumbnail_url" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="thumbnail_public_id" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="video_trailer_url" Type="nvarchar" MaxLength="255" Nullable="false" />
    <Property Name="video_public_id" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="created_at" Type="datetime" />
    <Property Name="updated_at" Type="datetime" />
    <Property Name="rate" Type="decimal" Precision="3" Scale="1" />
    <Property Name="duration" Type="int" Nullable="false" />
    <Property Name="is_deleted" Type="bit" />
  </EntityType>
  <EntityType Name="prices">
    <Key>
      <PropertyRef Name="price_id" />
    </Key>
    <Property Name="price_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="is_vip" Type="bit" Nullable="false" />
    <Property Name="price_value" Type="decimal" Precision="10" Scale="2" Nullable="false" />
    <Property Name="ticket_type_id" Type="int" Nullable="false" />
    <Property Name="created_at" Type="datetime" />
    <Property Name="updated_at" Type="datetime" />
  </EntityType>
  <EntityType Name="room_sections">
    <Key>
      <PropertyRef Name="section_id" />
    </Key>
    <Property Name="section_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="section_name" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="room_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="num_rows" Type="int" Nullable="false" />
    <Property Name="seat_per_row" Type="int" Nullable="false" />
    <Property Name="row_num_vip" Type="int" />
    <Property Name="start_row_num_vip" Type="int" />
    <Property Name="created_at" Type="datetime" />
    <Property Name="updated_at" Type="datetime" />
  </EntityType>
  <EntityType Name="rooms">
    <Key>
      <PropertyRef Name="room_id" />
    </Key>
    <Property Name="room_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="room_name" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="description" Type="nvarchar" MaxLength="500" />
    <Property Name="status" Type="nvarchar" MaxLength="20" Nullable="false" />
    <Property Name="capacity" Type="int" Nullable="false" />
    <Property Name="created_at" Type="datetime" />
    <Property Name="updated_at" Type="datetime" />
  </EntityType>
  <EntityType Name="schedules">
    <Key>
      <PropertyRef Name="schedule_id" />
    </Key>
    <Property Name="schedule_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="room_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="movie_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="show_time" Type="datetime2" Precision="7" Nullable="false" />
    <Property Name="created_at" Type="datetime2" Precision="7" />
    <Property Name="updated_at" Type="datetime2" Precision="7" />
  </EntityType>
  <EntityType Name="sysdiagrams">
    <Key>
      <PropertyRef Name="diagram_id" />
    </Key>
    <Property Name="name" Type="nvarchar" MaxLength="128" Nullable="false" />
    <Property Name="principal_id" Type="int" Nullable="false" />
    <Property Name="diagram_id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="version" Type="int" />
    <Property Name="definition" Type="varbinary(max)" />
  </EntityType>
  <EntityType Name="tickets">
    <Key>
      <PropertyRef Name="ticket_id" />
    </Key>
    <Property Name="ticket_id" Type="char" MaxLength="12" Nullable="false" />
    <Property Name="customer_id" Type="uniqueidentifier" />
    <Property Name="staff_id" Type="char" MaxLength="8" Nullable="false" />
    <Property Name="ticket_type_id" Type="int" Nullable="false" />
    <Property Name="seat_label" Type="nvarchar" MaxLength="10" Nullable="false" />
    <Property Name="price" Type="decimal" Precision="10" Scale="2" Nullable="false" />
    <Property Name="created_at" Type="datetime" />
    <Property Name="updated_at" Type="datetime" />
    <Property Name="payment" Type="int" Nullable="false" />
    <Property Name="ispaid" Type="bit" />
    <Property Name="schedule_id" Type="uniqueidentifier" Nullable="false" />
  </EntityType>
  <EntityType Name="types">
    <Key>
      <PropertyRef Name="type_id" />
    </Key>
    <Property Name="type_id" Type="uniqueidentifier" Nullable="false" />
    <Property Name="type_name" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="created_at" Type="datetime" />
    <Property Name="updated_at" Type="datetime" />
  </EntityType>
  <EntityType Name="users">
    <Key>
      <PropertyRef Name="user_id" />
    </Key>
    <Property Name="user_id" Type="char" MaxLength="8" Nullable="false" />
    <Property Name="user_name" Type="nvarchar" MaxLength="50" Nullable="false" />
    <Property Name="password" Type="nvarchar" MaxLength="100" Nullable="false" />
    <Property Name="cccd" Type="nvarchar" MaxLength="20" />
    <Property Name="roleid" Type="int" Nullable="false" />
    <Property Name="isdeleted" Type="bit" />
    <Property Name="created_at" Type="datetime" />
  </EntityType>
  <Association Name="FK_movie_attributes_movie">
    <End Role="movies" Type="Self.movies" Multiplicity="1" />
    <End Role="movie_attributes" Type="Self.movie_attributes" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="movie_attributes">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_movie_types_movie">
    <End Role="movies" Type="Self.movies" Multiplicity="1" />
    <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="movie_types">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_movie_types_type">
    <End Role="types" Type="Self.types" Multiplicity="1" />
    <End Role="movie_types" Type="Self.movie_types" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="types">
        <PropertyRef Name="type_id" />
      </Principal>
      <Dependent Role="movie_types">
        <PropertyRef Name="type_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_prices_movie">
    <End Role="movies" Type="Self.movies" Multiplicity="1" />
    <End Role="prices" Type="Self.prices" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="prices">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_room_sections_room">
    <End Role="rooms" Type="Self.rooms" Multiplicity="1" />
    <End Role="room_sections" Type="Self.room_sections" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="rooms">
        <PropertyRef Name="room_id" />
      </Principal>
      <Dependent Role="room_sections">
        <PropertyRef Name="room_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_schedules_movie">
    <End Role="movies" Type="Self.movies" Multiplicity="1" />
    <End Role="schedules" Type="Self.schedules" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="movies">
        <PropertyRef Name="movie_id" />
      </Principal>
      <Dependent Role="schedules">
        <PropertyRef Name="movie_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_schedules_room">
    <End Role="rooms" Type="Self.rooms" Multiplicity="1" />
    <End Role="schedules" Type="Self.schedules" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="rooms">
        <PropertyRef Name="room_id" />
      </Principal>
      <Dependent Role="schedules">
        <PropertyRef Name="room_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_tickets_customer">
    <End Role="customers" Type="Self.customers" Multiplicity="0..1" />
    <End Role="tickets" Type="Self.tickets" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="customers">
        <PropertyRef Name="customer_id" />
      </Principal>
      <Dependent Role="tickets">
        <PropertyRef Name="customer_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_tickets_schedules">
    <End Role="schedules" Type="Self.schedules" Multiplicity="1" />
    <End Role="tickets" Type="Self.tickets" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="schedules">
        <PropertyRef Name="schedule_id" />
      </Principal>
      <Dependent Role="tickets">
        <PropertyRef Name="schedule_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK_tickets_staff">
    <End Role="users" Type="Self.users" Multiplicity="1" />
    <End Role="tickets" Type="Self.tickets" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="users">
        <PropertyRef Name="user_id" />
      </Principal>
      <Dependent Role="tickets">
        <PropertyRef Name="staff_id" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Function Name="fn_diagramobjects" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="true" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" ReturnType="int" />
  <Function Name="sp_alterdiagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
    <Parameter Name="owner_id" Type="int" Mode="In" />
    <Parameter Name="version" Type="int" Mode="In" />
    <Parameter Name="definition" Type="varbinary(max)" Mode="In" />
  </Function>
  <Function Name="sp_creatediagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
    <Parameter Name="owner_id" Type="int" Mode="In" />
    <Parameter Name="version" Type="int" Mode="In" />
    <Parameter Name="definition" Type="varbinary(max)" Mode="In" />
  </Function>
  <Function Name="sp_dropdiagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
    <Parameter Name="owner_id" Type="int" Mode="In" />
  </Function>
  <Function Name="sp_helpdiagramdefinition" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
    <Parameter Name="owner_id" Type="int" Mode="In" />
  </Function>
  <Function Name="sp_helpdiagrams" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
    <Parameter Name="owner_id" Type="int" Mode="In" />
  </Function>
  <Function Name="sp_renamediagram" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
    <Parameter Name="diagramname" Type="nvarchar" Mode="In" />
    <Parameter Name="owner_id" Type="int" Mode="In" />
    <Parameter Name="new_diagramname" Type="nvarchar" Mode="In" />
  </Function>
  <Function Name="sp_upgraddiagrams" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo" />
  <EntityContainer Name="demo_wfsummerModelStoreContainer">
    <EntitySet Name="customers" EntityType="Self.customers" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="movie_attributes" EntityType="Self.movie_attributes" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="movie_types" EntityType="Self.movie_types" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="movies" EntityType="Self.movies" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="prices" EntityType="Self.prices" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="room_sections" EntityType="Self.room_sections" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="rooms" EntityType="Self.rooms" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="schedules" EntityType="Self.schedules" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagrams" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="tickets" EntityType="Self.tickets" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="types" EntityType="Self.types" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="users" EntityType="Self.users" Schema="dbo" store:Type="Tables" />
    <AssociationSet Name="FK_movie_attributes_movie" Association="Self.FK_movie_attributes_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="movie_attributes" EntitySet="movie_attributes" />
    </AssociationSet>
    <AssociationSet Name="FK_movie_types_movie" Association="Self.FK_movie_types_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="movie_types" EntitySet="movie_types" />
    </AssociationSet>
    <AssociationSet Name="FK_movie_types_type" Association="Self.FK_movie_types_type">
      <End Role="types" EntitySet="types" />
      <End Role="movie_types" EntitySet="movie_types" />
    </AssociationSet>
    <AssociationSet Name="FK_prices_movie" Association="Self.FK_prices_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="prices" EntitySet="prices" />
    </AssociationSet>
    <AssociationSet Name="FK_room_sections_room" Association="Self.FK_room_sections_room">
      <End Role="rooms" EntitySet="rooms" />
      <End Role="room_sections" EntitySet="room_sections" />
    </AssociationSet>
    <AssociationSet Name="FK_schedules_movie" Association="Self.FK_schedules_movie">
      <End Role="movies" EntitySet="movies" />
      <End Role="schedules" EntitySet="schedules" />
    </AssociationSet>
    <AssociationSet Name="FK_schedules_room" Association="Self.FK_schedules_room">
      <End Role="rooms" EntitySet="rooms" />
      <End Role="schedules" EntitySet="schedules" />
    </AssociationSet>
    <AssociationSet Name="FK_tickets_customer" Association="Self.FK_tickets_customer">
      <End Role="customers" EntitySet="customers" />
      <End Role="tickets" EntitySet="tickets" />
    </AssociationSet>
    <AssociationSet Name="FK_tickets_schedules" Association="Self.FK_tickets_schedules">
      <End Role="schedules" EntitySet="schedules" />
      <End Role="tickets" EntitySet="tickets" />
    </AssociationSet>
    <AssociationSet Name="FK_tickets_staff" Association="Self.FK_tickets_staff">
      <End Role="users" EntitySet="users" />
      <End Role="tickets" EntitySet="tickets" />
    </AssociationSet>
  </EntityContainer>
</Schema>