﻿using System;
using System.Drawing;
using System.Windows.Forms;
using WF_CinemaSummer2025.Repositories;
using WF_CinemaSummer2025.Services;

namespace WF_CinemaSummer2025.UIs
{
    public partial class MainForm : Form
    {
        private user currentUser;
        private UserService userService;
        private bool sidebarExpanded = true;
        private const int SIDEBAR_EXPANDED_WIDTH = 250;
        private const int SIDEBAR_COLLAPSED_WIDTH = 0; 

        public MainForm(user loggedInUser)
        {
            InitializeComponent();
            currentUser = loggedInUser;
            userService = new UserService();
            
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            
            InitializeUI();
        }

        private void InitializeUI()
        {
            // Set user info
            lblUserName.Text = currentUser.user_name;
            lblUserRole.Text = userService.GetUserRole(currentUser);

            // Set role-based permissions
            SetRoleBasedPermissions();

            // Initialize sidebar state
            panelSidebar.Width = SIDEBAR_EXPANDED_WIDTH;
            UpdateToggleButtonPosition();

            // Set form title
            this.Text = $"Cinema Management System - {userService.GetUserRole(currentUser)}";
        }

        private void SetRoleBasedPermissions()
        {
            // Admin có quyền truy cập tất cả
            if (userService.IsAdmin(currentUser))
            {
                btnRoomManagement.Visible = true;
                btnMovieManagement.Visible = true;
            }
            // Staff chỉ có quyền quản lý phim
            else if (userService.IsStaff(currentUser))
            {
                btnRoomManagement.Visible = false;
                btnMovieManagement.Visible = true;
            }
        }

        private void btnToggleSidebar_Click(object sender, EventArgs e)
        {
            ToggleSidebar();
        }

        private void ToggleSidebar()
        {
            Timer timer = new Timer();
            timer.Interval = 1; // Rất nhanh

            int targetWidth = sidebarExpanded ? SIDEBAR_COLLAPSED_WIDTH : SIDEBAR_EXPANDED_WIDTH;
            int step = sidebarExpanded ? -50 : 50; // Bước nhảy lớn hơn

            timer.Tick += (s, e) =>
            {
                panelSidebar.Width += step;

                if ((step > 0 && panelSidebar.Width >= targetWidth) ||
                    (step < 0 && panelSidebar.Width <= targetWidth))
                {
                    panelSidebar.Width = targetWidth;
                    timer.Stop();
                    timer.Dispose();

                    sidebarExpanded = !sidebarExpanded;
                    UpdateSidebarLabels();
                    UpdateToggleButtonPosition();
                }
            };

            timer.Start();
        }

        private void UpdateSidebarLabels()
        {
            lblUserName.Visible = sidebarExpanded;
            lblUserRole.Visible = sidebarExpanded;
            lblTitle.Visible = sidebarExpanded;

            btnRoomManagement.Visible = sidebarExpanded;
            btnMovieManagement.Visible = sidebarExpanded;
            btnLogout.Visible = sidebarExpanded;
            guna2CirclePictureBox1.Visible = sidebarExpanded;
            guna2Separator1.Visible = sidebarExpanded;

            if (sidebarExpanded)
            {
                btnRoomManagement.Text = "🏢  Quản lý Phòng";
                btnMovieManagement.Text = "🎬  Quản lý Phim";
                btnLogout.Text = "🚪  Đăng xuất";
                btnToggleSidebar.Text = "◀";
            }
            else
            {
                btnToggleSidebar.Text = "▶";
            }
        }

        private void UpdateToggleButtonPosition()
        {
            if (sidebarExpanded)
            {
                // Khi sidebar mở rộng, nút toggle ở bên phải sidebar
                btnToggleSidebar.Location = new System.Drawing.Point(20, 15);
            }
            else
            {
                // Khi sidebar thu gọn, nút toggle ở góc trái trên cùng
                btnToggleSidebar.Location = new System.Drawing.Point(10, 15);
            }
        }

        private void btnRoomManagement_Click(object sender, EventArgs e)
        {
            try
            {
                // Ẩn MainForm
                this.Hide();

                // Mở RoomListForm
                Room.RoomListForm roomForm = new Room.RoomListForm(currentUser);

                // Khi RoomListForm đóng thì hiện lại MainForm
                roomForm.FormClosed += (s, args) => {
                    this.Show();
                };

                roomForm.Show();
            }
            catch (Exception ex)
            {
                this.Show(); // Đảm bảo MainForm hiện lại nếu có lỗi
                MessageBox.Show($"Lỗi khi mở form quản lý phòng: {ex.Message}", "Lỗi",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        // private void LoadUserControlToMainPanel(UserControl userControl, string title)
        // {
        //     // Clear existing controls
        //     panelMain.Controls.Clear();
        //
        //     // Update title
        //     lblTitle.Text = title;
        //
        //     // Add new user control
        //     userControl.Dock = DockStyle.Fill;
        //     panelMain.Controls.Add(userControl);
        //
        //     // Initialize if it's RoomListUserControl
        //     if (userControl is Room.RoomListUserControl roomControl)
        //     {
        //         roomControl.Initialize(currentUser);
        //
        //         // Force layout refresh after a short delay
        //         this.BeginInvoke(new Action(() => {
        //             roomControl.RefreshLayout();
        //         }));
        //     }
        // }

        private void btnMovieManagement_Click(object sender, EventArgs e)
        {
            // TODO: Mở form quản lý phim
            MessageBox.Show("Chức năng Quản lý Phim sẽ được phát triển!", "Thông báo", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Bạn có chắc chắn muốn đăng xuất?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
            if (result == DialogResult.Yes)
            {
                this.Hide();
                
                // Mở lại form đăng nhập
                using (var loginForm = new Auth.LoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // Cập nhật user mới
                        currentUser = loginForm.LoggedInUser;
                        InitializeUI();
                        this.Show();
                    }
                    else
                    {
                        // Nếu không đăng nhập lại, thoát ứng dụng
                        Application.Exit();
                    }
                }
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("Bạn có chắc chắn muốn thoát ứng dụng?", "Xác nhận", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            userService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
