using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Smooth Loading Spinner không có viền vuông, hiệu ứng mượt mà
    /// </summary>
    public class SmoothLoadingSpinner : Control
    {
        private Timer animationTimer;
        private float rotationAngle = 0f;
        private Color primaryColor = Color.FromArgb(52, 152, 219);
        private int numberOfDots = 12;
        private float dotSize = 3f;
        private float radius = 20f;
        private bool isAnimating = false;

        public SmoothLoadingSpinner()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw |
                     ControlStyles.SupportsTransparentBackColor, true);
            
            this.BackColor = Color.Transparent;
            this.Size = new Size(50, 50);
            
            // Initialize timer
            animationTimer = new Timer();
            animationTimer.Interval = 80; // 80ms for smooth animation
            animationTimer.Tick += AnimationTimer_Tick;
        }

        #region Properties

        public Color PrimaryColor
        {
            get { return primaryColor; }
            set { primaryColor = value; Invalidate(); }
        }

        public int NumberOfDots
        {
            get { return numberOfDots; }
            set { numberOfDots = Math.Max(6, Math.Min(20, value)); Invalidate(); }
        }

        public float DotSize
        {
            get { return dotSize; }
            set { dotSize = Math.Max(1f, Math.Min(8f, value)); Invalidate(); }
        }

        public bool IsAnimating
        {
            get { return isAnimating; }
        }

        #endregion

        #region Methods

        public void Start()
        {
            if (!isAnimating)
            {
                isAnimating = true;
                animationTimer.Start();
            }
        }

        public void Stop()
        {
            if (isAnimating)
            {
                isAnimating = false;
                animationTimer.Stop();
                rotationAngle = 0f;
                Invalidate();
            }
        }

        #endregion

        #region Event Handlers

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            rotationAngle += 30f; // Rotate 30 degrees each tick
            if (rotationAngle >= 360f)
                rotationAngle = 0f;
            
            Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.CompositingQuality = CompositingQuality.HighQuality;
            
            // Clear background
            g.Clear(Color.Transparent);
            
            if (!isAnimating && rotationAngle == 0f)
                return;
            
            // Calculate center
            PointF center = new PointF(Width / 2f, Height / 2f);
            
            // Calculate radius based on control size
            float currentRadius = Math.Min(Width, Height) / 2f - dotSize - 2f;
            
            // Draw dots
            for (int i = 0; i < numberOfDots; i++)
            {
                float angle = (360f / numberOfDots * i + rotationAngle) * (float)Math.PI / 180f;
                
                // Calculate dot position
                float x = center.X + currentRadius * (float)Math.Cos(angle);
                float y = center.Y + currentRadius * (float)Math.Sin(angle);
                
                // Calculate alpha based on position (fade effect)
                float alpha = GetAlphaForDot(i);
                
                // Create brush with calculated alpha
                Color dotColor = Color.FromArgb(
                    (int)(255 * alpha),
                    primaryColor.R,
                    primaryColor.G,
                    primaryColor.B
                );
                
                using (SolidBrush brush = new SolidBrush(dotColor))
                {
                    float dotDiameter = dotSize * 2;
                    g.FillEllipse(brush, 
                        x - dotSize, 
                        y - dotSize, 
                        dotDiameter, 
                        dotDiameter);
                }
            }
        }

        private float GetAlphaForDot(int dotIndex)
        {
            if (!isAnimating)
                return 0.3f;
            
            // Calculate which dot should be brightest based on rotation
            float normalizedRotation = (rotationAngle % 360f) / 360f;
            float dotPosition = (float)dotIndex / numberOfDots;
            
            // Calculate distance from the "active" position
            float distance = Math.Abs(normalizedRotation - dotPosition);
            if (distance > 0.5f)
                distance = 1f - distance;
            
            // Convert distance to alpha (closer = brighter)
            float alpha = 1f - (distance * 2f);
            return Math.Max(0.15f, Math.Min(1f, alpha));
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            Invalidate();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                animationTimer?.Stop();
                animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }

    /// <summary>
    /// Loading Panel với smooth spinner và text, không có viền
    /// </summary>
    public class SmoothLoadingPanel : Panel
    {
        private SmoothLoadingSpinner spinner;
        private Label loadingLabel;
        private string loadingText = "Đang xử lý...";

        public SmoothLoadingPanel()
        {
            InitializeComponents();
            SetupPanel();
        }

        private void InitializeComponents()
        {
            // Create spinner
            spinner = new SmoothLoadingSpinner
            {
                Size = new Size(50, 50),
                PrimaryColor = Color.FromArgb(52, 152, 219),
                NumberOfDots = 12,
                DotSize = 3f
            };

            // Create label
            loadingLabel = new Label
            {
                Text = loadingText,
                Font = new Font("Segoe UI", 11F, FontStyle.Regular),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                AutoSize = false,
                BackColor = Color.Transparent
            };

            // Add to panel
            this.Controls.Add(spinner);
            this.Controls.Add(loadingLabel);
        }

        private void SetupPanel()
        {
            this.BackColor = Color.White;
            this.Size = new Size(250, 140);
            this.Visible = false;
            this.BorderStyle = BorderStyle.None;
            
            // Position controls
            PositionControls();
        }

        private void PositionControls()
        {
            // Center spinner
            spinner.Location = new Point(
                (Width - spinner.Width) / 2,
                30
            );

            // Position label below spinner
            loadingLabel.Size = new Size(Width - 20, 30);
            loadingLabel.Location = new Point(10, 95);
        }

        public string LoadingText
        {
            get { return loadingText; }
            set
            {
                loadingText = value;
                if (loadingLabel != null)
                    loadingLabel.Text = value;
            }
        }

        public void StartLoading(string text = null)
        {
            if (!string.IsNullOrEmpty(text))
                LoadingText = text;
            
            spinner?.Start();
            this.Visible = true;
            this.BringToFront();
        }

        public void StopLoading()
        {
            spinner?.Stop();
            this.Visible = false;
        }

        protected override void OnResize(EventArgs eventargs)
        {
            base.OnResize(eventargs);
            if (spinner != null && loadingLabel != null)
                PositionControls();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                spinner?.Stop();
                spinner?.Dispose();
                loadingLabel?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
