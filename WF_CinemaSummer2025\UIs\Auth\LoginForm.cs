﻿﻿using System;
using System.Drawing;
using System.Windows.Forms;
using WF_CinemaSummer2025.Services;
using WF_CinemaSummer2025.Repositories;

namespace WF_CinemaSummer2025.UIs.Auth
{
    public partial class LoginForm : Form
    {
        private UserService userService;
        public user LoggedInUser { get; private set; }

        public LoginForm()
        {
            InitializeComponent();
            userService = new UserService();
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text;

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                ShowMessage("Vui lòng nhập đầy đủ thông tin!", false);
                return;
            }

            // Show loading
            btnLogin.Text = "Đang đăng nhập...";
            btnLogin.Enabled = false;

            try
            {
                LoggedInUser = userService.Login(username, password);

                if (LoggedInUser != null)
                {
                    ShowMessage($"Đăng nhập thành công! Chào mừng {userService.GetUserRole(LoggedInUser)}", true);
                    
                    // Delay một chút để user thấy thông báo
                    Timer timer = new Timer();
                    timer.Interval = 1500;
                    timer.Tick += (s, args) =>
                    {
                        timer.Stop();
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    };
                    timer.Start();
                }
                else
                {
                    ShowMessage("Tên đăng nhập hoặc mật khẩu không đúng!", false);
                    ResetLoginButton();
                }
            }
            catch (Exception ex)
            {
                ShowMessage("Có lỗi xảy ra khi đăng nhập!", false);
                ResetLoginButton();
            }
        }

        private void ResetLoginButton()
        {
            btnLogin.Text = "ĐĂNG NHẬP";
            btnLogin.Enabled = true;
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            lblMessage.Text = message;
            lblMessage.ForeColor = isSuccess ? Color.Green : Color.Red;
            lblMessage.Visible = true;

            // Ẩn message sau 3 giây
            Timer timer = new Timer();
            timer.Interval = 3000;
            timer.Tick += (s, e) =>
            {
                lblMessage.Visible = false;
                timer.Stop();
            };
            timer.Start();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void txtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }

        private void txtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
            }
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // Seed data khi form load
            try
            {
                var seeder = new DatabaseSeeder();
                seeder.SeedAll();
                seeder.Dispose();
            }
            catch (Exception ex)
            {
                // Nếu có lỗi database, hiển thị thông báo nhưng vẫn cho phép thử đăng nhập
                ShowMessage("Không thể kết nối database. Vui lòng kiểm tra cấu hình!", false);
            }

            txtUsername.Focus();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            userService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
