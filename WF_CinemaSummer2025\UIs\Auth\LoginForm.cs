﻿﻿using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using WF_CinemaSummer2025.Services;
using WF_CinemaSummer2025.Repositories;

namespace WF_CinemaSummer2025.UIs.Auth
{
    public partial class LoginForm : Form
    {
        private UserService userService;
        public user LoggedInUser { get; private set; }

        public LoginForm()
        {
            InitializeComponent();
            userService = new UserService();
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text;

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                ShowMessage("Vui lòng nhập đầy đủ thông tin!", false);
                return;
            }

            // Show loading spinner
            ShowLoadingSpinner(true, "<PERSON>ang đăng nhập...");

            try
            {
                // Simulate network delay để thấy rõ loading effect
                await Task.Delay(1500);

                LoggedInUser = userService.Login(username, password);

                if (LoggedInUser != null)
                {
                    // Update loading text
                    UpdateLoadingText("Đăng nhập thành công!");
                    await Task.Delay(800);

                    ShowMessage($"Đăng nhập thành công! Chào mừng {userService.GetUserRole(LoggedInUser)}", true);

                    // Hide loading and delay một chút để user thấy thông báo
                    ShowLoadingSpinner(false);

                    Timer timer = new Timer();
                    timer.Interval = 1500;
                    timer.Tick += (s, args) =>
                    {
                        timer.Stop();
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    };
                    timer.Start();
                }
                else
                {
                    ShowLoadingSpinner(false);
                    ShowMessage("Tên đăng nhập hoặc mật khẩu không đúng!", false);
                }
            }
            catch (Exception ex)
            {
                ShowLoadingSpinner(false);
                ShowMessage("Có lỗi xảy ra khi đăng nhập!", false);
            }
        }

        private void ResetLoginButton()
        {
            btnLogin.Text = "ĐĂNG NHẬP";
            btnLogin.Enabled = true;
        }

        private void ShowLoadingSpinner(bool show, string loadingText = "Đang xử lý...")
        {
            if (show)
            {
                // Disable form controls
                btnLogin.Enabled = false;
                btnExit.Enabled = false;
                txtUsername.Enabled = false;
                txtPassword.Enabled = false;

                // Update loading text
                lblLoadingText.Text = loadingText;

                // Show loading panel
                loadingPanel.Visible = true;
                loadingPanel.BringToFront();

                // Start spinner animation
                loadingSpinner.Start();
            }
            else
            {
                // Hide loading panel
                loadingPanel.Visible = false;

                // Stop spinner animation
                loadingSpinner.Stop();

                // Re-enable form controls
                btnLogin.Enabled = true;
                btnExit.Enabled = true;
                txtUsername.Enabled = true;
                txtPassword.Enabled = true;
            }
        }

        private void UpdateLoadingText(string text)
        {
            if (loadingPanel.Visible)
            {
                lblLoadingText.Text = text;
            }
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            lblMessage.Text = message;
            lblMessage.ForeColor = isSuccess ? Color.Green : Color.Red;
            lblMessage.Visible = true;

            // Ẩn message sau 3 giây
            Timer timer = new Timer();
            timer.Interval = 3000;
            timer.Tick += (s, e) =>
            {
                lblMessage.Visible = false;
                timer.Stop();
            };
            timer.Start();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void txtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }

        private void txtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
            }
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // Seed data khi form load
            try
            {
                var seeder = new DatabaseSeeder();
                seeder.SeedAll();
                seeder.Dispose();
            }
            catch (Exception ex)
            {
                // Nếu có lỗi database, hiển thị thông báo nhưng vẫn cho phép thử đăng nhập
                ShowMessage("Không thể kết nối database. Vui lòng kiểm tra cấu hình!", false);
            }

            txtUsername.Focus();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            userService?.Dispose();
            base.OnFormClosed(e);
        }

        private void loadingPanel_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
