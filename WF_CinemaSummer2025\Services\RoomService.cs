﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using WF_CinemaSummer2025.Repositories;

namespace WF_CinemaSummer2025.Services
{
    public class RoomService
    {
        private demo_wfsummerEntities db;

        public RoomService()
        {
            db = new demo_wfsummerEntities();
        }

        /// <summary>
        /// L<PERSON>y tất cả phòng
        /// </summary>
        /// <returns>Danh sách phòng</returns>
        public List<room> GetAllRooms()
        {
            try
            {
                return db.rooms.OrderBy(r => r.room_name).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi lấy danh sách phòng: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy phòng với phân trang
        /// </summary>
        /// <param name="pageNumber">Số trang (bắt đầu từ 1)</param>
        /// <param name="pageSize">Số item trên mỗi trang</param>
        /// <param name="searchText">Từ khóa tìm kiếm</param>
        /// <param name="statusFilter"><PERSON><PERSON><PERSON> theo trạng thái</param>
        /// <returns>Tuple chứa danh sách phòng và tổng số records</returns>
        public (List<room> rooms, int totalRecords) GetRoomsWithPaging(int pageNumber, int pageSize, string searchText = "", string statusFilter = "")
        {
            try
            {
                var query = db.rooms.AsQueryable();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(searchText))
                {
                    searchText = searchText.ToLower();
                    query = query.Where(r => r.room_name.ToLower().Contains(searchText) ||
                                           (r.description != null && r.description.ToLower().Contains(searchText)));
                }

                // Apply status filter
                if (!string.IsNullOrWhiteSpace(statusFilter) && statusFilter != "Tất cả")
                {
                    query = query.Where(r => r.status == statusFilter);
                }

                // Get total count before paging
                int totalRecords = query.Count();

                // Apply paging - sắp xếp theo updated_at mới nhất trước
                var rooms = query.OrderByDescending(r => r.updated_at)
                                .Skip((pageNumber - 1) * pageSize)
                                .Take(pageSize)
                                .ToList();

                return (rooms, totalRecords);
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi lấy danh sách phòng với phân trang: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy phòng theo ID
        /// </summary>
        /// <param name="roomId">ID phòng</param>
        /// <returns>Thông tin phòng</returns>
        public room GetRoomById(Guid roomId)
        {
            try
            {
                return db.rooms.FirstOrDefault(r => r.room_id == roomId);
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi lấy thông tin phòng: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy phòng theo trạng thái
        /// </summary>
        /// <param name="status">Trạng thái phòng</param>
        /// <returns>Danh sách phòng theo trạng thái</returns>
        public List<room> GetRoomsByStatus(string status)
        {
            try
            {
                return db.rooms.Where(r => r.status == status)
                              .OrderBy(r => r.room_name)
                              .ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi lấy danh sách phòng theo trạng thái: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm phòng mới
        /// </summary>
        /// <param name="room">Thông tin phòng</param>
        /// <returns>True nếu thành công</returns>
        public bool AddRoom(room room)
        {
            try
            {
                room.room_id = Guid.NewGuid();
                room.created_at = DateTime.Now;
                room.updated_at = DateTime.Now;

                db.rooms.Add(room);
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi thêm phòng: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm phòng mới cùng với các sections
        /// </summary>
        /// <param name="newRoom">Thông tin phòng</param>
        /// <param name="sections">Danh sách sections</param>
        /// <returns>True nếu thành công</returns>
        public bool AddRoomWithSections(room newRoom, List<room_sections> sections)
        {
            using (var transaction = db.Database.BeginTransaction())
            {
                try
                {
                    // Add room first
                    db.rooms.Add(newRoom);
                    db.SaveChanges();

                    // Add room sections
                    foreach (var section in sections)
                    {
                        section.room_id = newRoom.room_id;
                        section.created_at = DateTime.Now;
                        section.updated_at = DateTime.Now;
                        db.room_sections.Add(section);
                    }

                    db.SaveChanges();
                    transaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    System.Diagnostics.Debug.WriteLine($"Error adding room with sections: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Cập nhật thông tin phòng
        /// </summary>
        /// <param name="room">Thông tin phòng</param>
        /// <returns>True nếu thành công</returns>
        public bool UpdateRoom(room room)
        {
            try
            {
                var existingRoom = db.rooms.FirstOrDefault(r => r.room_id == room.room_id);
                if (existingRoom == null)
                    return false;

                existingRoom.room_name = room.room_name;
                existingRoom.description = room.description;
                existingRoom.status = room.status;
                existingRoom.capacity = room.capacity;
                existingRoom.updated_at = DateTime.Now;

                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi cập nhật phòng: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa phòng
        /// </summary>
        /// <param name="roomId">ID phòng</param>
        /// <returns>True nếu thành công</returns>
        public bool DeleteRoom(Guid roomId)
        {
            try
            {
                var room = db.rooms.FirstOrDefault(r => r.room_id == roomId);
                if (room == null)
                    return false;

                // Kiểm tra xem phòng có đang được sử dụng không
                var hasSchedules = db.schedules.Any(s => s.room_id == roomId);
                if (hasSchedules)
                {
                    throw new Exception("Không thể xóa phòng đang có lịch chiếu!");
                }

                db.rooms.Remove(room);
                db.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi xóa phòng: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra tên phòng đã tồn tại chưa
        /// </summary>
        /// <param name="roomName">Tên phòng</param>
        /// <param name="excludeRoomId">ID phòng cần loại trừ (dùng khi update)</param>
        /// <returns>True nếu đã tồn tại</returns>
        public bool IsRoomNameExists(string roomName, Guid? excludeRoomId = null)
        {
            try
            {
                var query = db.rooms.Where(r => r.room_name.ToLower() == roomName.ToLower());
                
                if (excludeRoomId.HasValue)
                {
                    query = query.Where(r => r.room_id != excludeRoomId.Value);
                }

                return query.Any();
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi kiểm tra tên phòng: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy thống kê phòng theo trạng thái
        /// </summary>
        /// <returns>Dictionary với key là trạng thái, value là số lượng</returns>
        public Dictionary<string, int> GetRoomStatistics()
        {
            try
            {
                return db.rooms.GroupBy(r => r.status)
                              .ToDictionary(g => g.Key, g => g.Count());
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi khi lấy thống kê phòng: {ex.Message}");
            }
        }

        public void Dispose()
        {
            db?.Dispose();
        }
    }
}
