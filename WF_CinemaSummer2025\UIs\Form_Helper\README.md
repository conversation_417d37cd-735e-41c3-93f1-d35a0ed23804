# MessageForm - Custom Message Dialog

Một component tái sử dụng để hiển thị các thông báo đẹp mắt thay thế cho MessageBox mặc định của Windows Forms.

## Tính năng

- **5 loại message khác nhau**: Information, Success, Warning, Error, Question
- **<PERSON>ia<PERSON> diện đẹp mắt**: Sử dụng Guna UI với màu sắc phân biệt theo loại message
- **Tái sử dụng**: <PERSON><PERSON> thể gọi từ bất kỳ đâu trong ứng dụng
- **Dễ sử dụng**: API đơn giản tương tự MessageBox
- **Shadow effect**: Hiệu ứng đổ bóng chuyên nghiệp
- **Responsive**: Tự động căn giữa và hiển thị phù hợp

## Các loại Message

### 1. Information (Thông báo)
- **<PERSON><PERSON><PERSON> sắc**: <PERSON><PERSON><PERSON> (#17A2B8)
- **Icon**: ℹ
- **Button**: Chỉ có nút "OK"
- **Sử dụng**: Hi<PERSON>n thị thông tin chung

### 2. Success (Thành công)
- **Màu sắc**: Xanh lá (#28A745)
- **Icon**: ✓
- **Button**: Chỉ có nút "OK"
- **Sử dụng**: Thông báo thao tác thành công

### 3. Warning (Cảnh báo)
- **Màu sắc**: Vàng cam (#FFC107)
- **Icon**: ⚠
- **Button**: "Có" và "Không"
- **Sử dụng**: Cảnh báo người dùng trước khi thực hiện hành động

### 4. Error (Lỗi)
- **Màu sắc**: Đỏ (#DC3545)
- **Icon**: ✕
- **Button**: Chỉ có nút "OK"
- **Sử dụng**: Thông báo lỗi

### 5. Question (Câu hỏi)
- **Màu sắc**: Xanh dương đậm (#007BFF)
- **Icon**: ?
- **Button**: "Có" và "Không"
- **Sử dụng**: Xác nhận hành động từ người dùng

## Cách sử dụng

### Cách 1: Sử dụng static methods (Khuyến nghị)

```csharp
// Thông báo thành công
messageForm.ShowSuccess("Dữ liệu đã được lưu thành công!");

// Thông báo lỗi
messageForm.ShowError("Không thể kết nối đến cơ sở dữ liệu!");

// Cảnh báo với lựa chọn
var result = messageForm.ShowWarning("Bạn có chắc chắn muốn xóa?");
if (result == messageForm.MessageResult.Yes)
{
    // Thực hiện xóa
}

// Câu hỏi xác nhận
var result = messageForm.ShowQuestion("Bạn có muốn lưu thay đổi?");

// Thông báo thông tin
messageForm.ShowInformation("Hệ thống sẽ bảo trì từ 2:00-4:00 AM");
```

### Cách 2: Sử dụng với parent form

```csharp
// Hiển thị với parent form để căn giữa chính xác
messageForm.ShowSuccess(this, "Thao tác thành công!");
messageForm.ShowError(parentForm, "Đã xảy ra lỗi!");
```

### Cách 3: Sử dụng constructor trực tiếp

```csharp
using (var form = new messageForm())
{
    form.SetMessage("Nội dung tùy chỉnh", messageForm.MessageType.Warning);
    form.Text = "Title tùy chỉnh";
    var result = form.ShowDialog();
    
    if (form.Result == messageForm.MessageResult.Yes)
    {
        // Xử lý kết quả
    }
}
```

## Thay thế MessageBox

Thay vì sử dụng MessageBox truyền thống:

```csharp
// Cũ
MessageBox.Show("Thông báo", "Title", MessageBoxButtons.OK, MessageBoxIcon.Information);
MessageBox.Show("Lỗi", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
var result = MessageBox.Show("Xác nhận?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

// Mới - Đẹp hơn và nhất quán
messageForm.ShowInformation("Thông báo", "Title");
messageForm.ShowError("Lỗi", "Error");
var result = messageForm.ShowQuestion("Xác nhận?", "Confirm");
```

## Kết quả trả về

```csharp
public enum MessageResult
{
    Yes,    // Người dùng chọn "Có"
    No,     // Người dùng chọn "Không"
    OK,     // Người dùng chọn "OK"
    None    // Không có lựa chọn (mặc định)
}
```

## Ví dụ thực tế

Xem file `example.cs` để có các ví dụ chi tiết về cách sử dụng trong các tình huống khác nhau.

## Tùy chỉnh

Bạn có thể tùy chỉnh thêm bằng cách:
1. Chỉnh sửa màu sắc trong các method `SetupXXXStyle()`
2. Thay đổi font chữ và kích thước trong Designer
3. Thêm animation hoặc hiệu ứng khác
4. Mở rộng thêm các loại message mới

## Lưu ý

- Form sử dụng `FormBorderStyle.None` và `Guna2Elipse` để tạo góc bo tròn
- Có `Guna2ShadowForm` để tạo hiệu ứng đổ bóng
- Tự động căn giữa với parent form hoặc màn hình
- Sử dụng `TopMost = true` để luôn hiển thị trên cùng
