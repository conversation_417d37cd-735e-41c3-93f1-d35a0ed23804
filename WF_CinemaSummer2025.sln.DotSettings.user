﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:Boolean x:Key="/Default/Environment/Hierarchy/EntityFrameworkOptions/IsAlreadyNotifiedAboutEntityFramework/@EntryValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/ResxEditorPersonal/CheckedGroups/=WF_005FCinemaSummer2025_002FUIs_002FAuth_002FLoginForm/@EntryIndexedValue">False</s:Boolean>
	<s:Boolean x:Key="/Default/ResxEditorPersonal/CheckedGroups/=WF_005FCinemaSummer2025_002FUIs_002FMainForm/@EntryIndexedValue">False</s:Boolean>
	<s:Boolean x:Key="/Default/ResxEditorPersonal/CheckedGroups/=WF_005FCinemaSummer2025_002FUIs_002FRoom_002FAddRoomForm/@EntryIndexedValue">True</s:Boolean>
	
	<s:Boolean x:Key="/Default/ResxEditorPersonal/CheckedGroups/=WF_005FCinemaSummer2025_002FUIs_002FRoom_002FRoomListForm/@EntryIndexedValue">False</s:Boolean>
	<s:Boolean x:Key="/Default/ResxEditorPersonal/CheckedGroups/=WF_005FCinemaSummer2025_002FUIs_002FRoom_002FRoomListUserControl/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/ResxEditorPersonal/Initialized/@EntryValue">True</s:Boolean></wpf:ResourceDictionary>