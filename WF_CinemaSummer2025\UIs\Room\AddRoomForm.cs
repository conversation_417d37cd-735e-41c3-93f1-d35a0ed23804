﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WF_CinemaSummer2025.Services;
using WF_CinemaSummer2025.Repositories;
using System.Drawing.Drawing2D;

namespace WF_CinemaSummer2025.UIs.Room
{
    public partial class AddRoomForm : Form
    {
        private RoomService roomService;
        private user currentUser;
        private List<room_sections> roomSections;

        public AddRoomForm(user loggedInUser)
        {
            InitializeComponent();
            roomService = new RoomService();
            currentUser = loggedInUser;
            roomSections = new List<room_sections>();
            
            InitializeUI();
            //AddDefaultSection();
        }

        private void InitializeUI()
        {
            this.Text = "Thêm Phòng Chiếu";
            this.WindowState = FormWindowState.Maximized;
            
            // Setup form events
            this.Load += AddRoomForm_Load;
            
            // Setup control events
            btnAddSection.Click += BtnAddSection_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            
            // Set default status
            cmbStatus.SelectedIndex = 0; // "Hoạt động"
        }

        private void AddRoomForm_Load(object sender, EventArgs e)
        {
            RefreshSectionPreview();
        }
        
        private void BtnAddSection_Click(object sender, EventArgs e)
        {
            // Tạo section mới với tên tiếp theo (A, B, C, ...)
            char nextSectionName = (char)('A' + roomSections.Count);

            var newSection = new room_sections
            {
                section_id = Guid.NewGuid(),
                section_name = nextSectionName.ToString(),
                seat_per_row = 10,
                num_rows = 8,
                row_num_vip = 2,
                start_row_num_vip = 1,
                created_at = DateTime.Now,
                updated_at = DateTime.Now
            };

            roomSections.Add(newSection);
            RefreshSectionList();
            RefreshSectionPreview();
        }

        private void RefreshSectionList()
        {
            flowLayoutPanelSections.Controls.Clear();
            
            for (int i = 0; i < roomSections.Count; i++)
            {
                var section = roomSections[i];
                var sectionPanel = CreateSectionPanel(section, i);
                flowLayoutPanelSections.Controls.Add(sectionPanel);
            }
        }

        private Panel CreateSectionPanel(room_sections section, int index)
        {
            var panel = new Panel();
            panel.Size = new Size(450, 160);
            panel.BorderStyle = BorderStyle.FixedSingle;
            panel.Margin = new Padding(5);
            panel.BackColor = Color.FromArgb(248, 249, 250);

            // Section name với background đẹp
            var lblSection = new Label();
            lblSection.Text = $"🏛️ Section {section.section_name}";
            lblSection.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            lblSection.ForeColor = Color.FromArgb(52, 73, 94);
            lblSection.Location = new Point(15, 12);
            lblSection.AutoSize = true;

            // Row 1: Columns và Rows
            var lblColumns = new Label();
            lblColumns.Text = "Số cột:";
            lblColumns.Location = new Point(15, 45);
            lblColumns.Size = new Size(80, 20);
            lblColumns.Font = new Font("Segoe UI", 9F);

            var numColumns = new NumericUpDown();
            numColumns.Location = new Point(97, 43);
            numColumns.Size = new Size(70, 25);
            numColumns.Minimum = 1;
            numColumns.Maximum = 50;
            numColumns.Value = section.seat_per_row;
            numColumns.Font = new Font("Segoe UI", 9F);
            numColumns.ValueChanged += (s, e) => {
                section.seat_per_row = (int)numColumns.Value;
                section.updated_at = DateTime.Now;
                RefreshSectionPreview();
            };

            var lblRows = new Label();
            lblRows.Text = "Số hàng:";
            lblRows.Location = new Point(170, 45);
            lblRows.Size = new Size(90, 20);
            lblRows.Font = new Font("Segoe UI", 9F);

            var numRows = new NumericUpDown();
            numRows.Location = new Point(262, 43);
            numRows.Size = new Size(70, 25);
            numRows.Minimum = 1;
            numRows.Maximum = 50;
            numRows.Value = section.num_rows;
            numRows.Font = new Font("Segoe UI", 9F);
            numRows.ValueChanged += (s, e) => {
                section.num_rows = (int)numRows.Value;
                section.updated_at = DateTime.Now;
                RefreshSectionPreview();
            };

            // Row 2: VIP settings
            var lblVipRows = new Label();
            lblVipRows.Text = "Số hàng VIP:";
            lblVipRows.Location = new Point(15, 80);
            lblVipRows.Size = new Size(100, 20);
            lblVipRows.Font = new Font("Segoe UI", 9F);
            lblVipRows.ForeColor = Color.FromArgb(230, 126, 34);

            var numVipRows = new NumericUpDown();
            numVipRows.Location = new Point(116, 78);
            numVipRows.Size = new Size(60, 25);
            numVipRows.Minimum = 0;
            numVipRows.Maximum = 20;
            numVipRows.Value = section.row_num_vip ?? 0;
            numVipRows.Font = new Font("Segoe UI", 9F);
            numVipRows.ValueChanged += (s, e) => {
                section.row_num_vip = (int)numVipRows.Value;
                section.updated_at = DateTime.Now;
                // Validation
                if ((section.start_row_num_vip ?? 1) + (section.row_num_vip ?? 0) > section.num_rows)
                {
                    section.row_num_vip = Math.Max(0, section.num_rows - (section.start_row_num_vip ?? 1) + 1);
                    numVipRows.Value = section.row_num_vip ?? 0;
                }
                RefreshSectionPreview();
            };

            var lblVipStart = new Label();
            lblVipStart.Text = "Hàng VIP bắt đầu:";
            lblVipStart.Location = new Point(180, 80);
            lblVipStart.Size = new Size(135, 20);
            lblVipStart.Font = new Font("Segoe UI", 9F);
            lblVipStart.ForeColor = Color.FromArgb(230, 126, 34);

            var numVipStart = new NumericUpDown();
            numVipStart.Location = new Point(322, 78);
            numVipStart.Size = new Size(60, 25);
            numVipStart.Minimum = 1;
            numVipStart.Maximum = 50;
            numVipStart.Value = section.start_row_num_vip ?? 1;
            numVipStart.Font = new Font("Segoe UI", 9F);
            numVipStart.ValueChanged += (s, e) => {
                section.start_row_num_vip = (int)numVipStart.Value;
                section.updated_at = DateTime.Now;
                // Validation
                if ((section.start_row_num_vip ?? 1) > section.num_rows)
                {
                    section.start_row_num_vip = section.num_rows;
                    numVipStart.Value = section.start_row_num_vip ?? 1;
                }
                if ((section.start_row_num_vip ?? 1) + (section.row_num_vip ?? 0) > section.num_rows)
                {
                    section.row_num_vip = Math.Max(0, section.num_rows - (section.start_row_num_vip ?? 1) + 1);
                    numVipRows.Value = section.row_num_vip ?? 0;
                }
                RefreshSectionPreview();
            };
            
            // Summary info
            var lblSummary = new Label();
            int totalSeats = section.seat_per_row * section.num_rows;
            int vipSeats = (section.row_num_vip ?? 0) * section.seat_per_row;
            lblSummary.Text = $"📊 Tổng: {totalSeats} ghế (💺 {totalSeats - vipSeats} | 👑 {vipSeats})";
            lblSummary.Location = new Point(15, 115);
            lblSummary.Size = new Size(300, 20);
            lblSummary.Font = new Font("Segoe UI", 8.5F);
            lblSummary.ForeColor = Color.FromArgb(108, 117, 125);

            // Remove button (chỉ hiện nếu có > 1 section)
            if (roomSections.Count > 1)
            {
                var btnRemove = new Guna.UI2.WinForms.Guna2Button();
                btnRemove.Text = "🗑️ Xóa";
                btnRemove.Size = new Size(70, 30);
                btnRemove.Location = new Point(360, 110);
                btnRemove.FillColor = Color.FromArgb(231, 76, 60);
                btnRemove.Font = new Font("Segoe UI", 8F, FontStyle.Bold);
                btnRemove.ForeColor = Color.White;
                btnRemove.BorderRadius = 6;
                btnRemove.Click += (s, e) => {
                    roomSections.RemoveAt(index);
                    RefreshSectionList();
                    RefreshSectionPreview();
                };
                panel.Controls.Add(btnRemove);
            }
            
            panel.Controls.Add(lblSection);
            panel.Controls.Add(lblColumns);
            panel.Controls.Add(numColumns);
            panel.Controls.Add(lblRows);
            panel.Controls.Add(numRows);
            panel.Controls.Add(lblVipRows);
            panel.Controls.Add(numVipRows);
            panel.Controls.Add(lblVipStart);
            panel.Controls.Add(numVipStart);
            panel.Controls.Add(lblSummary);
            
            return panel;
        }

        private void RefreshSectionPreview()
        {
            panelPreview.Controls.Clear();
            
            int startX = 20;
            int currentX = startX;
            
            foreach (var section in roomSections)
            {
                var sectionPreview = CreateSectionPreview(section);
                sectionPreview.Location = new Point(currentX, 20);
                panelPreview.Controls.Add(sectionPreview);
                
                currentX += sectionPreview.Width + 30;
            }
        }

        private Panel CreateSectionPreview(room_sections section)
        {
            var panel = new Panel();
            panel.Size = new Size(section.seat_per_row * 35 + 60, section.num_rows * 30 + 80);
            panel.BorderStyle = BorderStyle.FixedSingle;
            panel.BackColor = Color.FromArgb(248, 249, 250);

            // Section title
            var lblTitle = new Label();
            lblTitle.Text = $"Section {section.section_name}";
            lblTitle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(52, 73, 94);
            lblTitle.Location = new Point(10, 8);
            lblTitle.AutoSize = true;
            panel.Controls.Add(lblTitle);

            // Create seat buttons với naming chính xác
            int seatNumber = 1;
            for (int row = 1; row <= section.num_rows; row++)
            {
                for (int col = 1; col <= section.seat_per_row; col++)
                {
                    var btnSeat = new Guna.UI2.WinForms.Guna2Button();
                    btnSeat.Size = new Size(30, 25);
                    btnSeat.Location = new Point(20 + (col - 1) * 35, 35 + (row - 1) * 30);

                    // Seat naming: 1, 2, 3, 4, 5, 6...
                    btnSeat.Text = seatNumber.ToString();
                    btnSeat.Font = new Font("Segoe UI", 8F, FontStyle.Bold);
                    btnSeat.BorderRadius = 4;

                    // Check if VIP seat - VIP rows tính TRONG tổng số rows
                    // Ví dụ: Tổng 8 rows, VIP 2 rows từ row 1 → rows 1,2 là VIP
                    bool isVip = (section.row_num_vip ?? 0) > 0 &&
                                row >= (section.start_row_num_vip ?? 1) &&
                                row < (section.start_row_num_vip ?? 1) + (section.row_num_vip ?? 0) &&
                                (section.start_row_num_vip ?? 1) + (section.row_num_vip ?? 0) <= section.num_rows;
                    
                    if (isVip)
                    {
                        // VIP seat - Luxurious gold color
                        btnSeat.FillColor = Color.FromArgb(255, 215, 0);    // Gold
                        btnSeat.ForeColor = Color.FromArgb(139, 69, 19);    // SaddleBrown
                        btnSeat.BorderColor = Color.FromArgb(184, 134, 11); // Dark gold border
                        btnSeat.BorderThickness = 2;

                        // Add shadow effect for luxury look
                        btnSeat.ShadowDecoration.Enabled = true;
                        btnSeat.ShadowDecoration.Color = Color.FromArgb(255, 193, 7);
                        btnSeat.ShadowDecoration.Depth = 3;
                    }
                    else
                    {
                        // Regular seat - Blue color
                        btnSeat.FillColor = Color.FromArgb(74, 144, 226);   // Light blue
                        btnSeat.ForeColor = Color.White;
                        btnSeat.BorderColor = Color.FromArgb(45, 85, 180);
                        btnSeat.BorderThickness = 1;

                        // Subtle shadow for regular seats
                        btnSeat.ShadowDecoration.Enabled = true;
                        btnSeat.ShadowDecoration.Color = Color.FromArgb(100, 56, 103, 214);
                        btnSeat.ShadowDecoration.Depth = 2;
                    }

                    // Hover effects
                    btnSeat.HoverState.FillColor = isVip ?
                        Color.FromArgb(255, 235, 59) :
                        Color.FromArgb(94, 164, 246);
                    
                    panel.Controls.Add(btnSeat);
                    seatNumber++;
                }
            }
            
            // Add legend - VIP rows tính TRONG tổng rows
            int totalSeats = section.seat_per_row * section.num_rows;
            int vipSeats = (section.row_num_vip ?? 0) > 0 &&
                          (section.start_row_num_vip ?? 1) + (section.row_num_vip ?? 0) <= section.num_rows ?
                          section.seat_per_row * (section.row_num_vip ?? 0) : 0;
            int regularSeats = totalSeats - vipSeats;

            var lblLegend = new Label();
            lblLegend.Text = $"💺 Regular: {regularSeats} | 👑 VIP: {vipSeats} | Total: {totalSeats}";
            lblLegend.Font = new Font("Segoe UI", 8F);
            lblLegend.ForeColor = Color.FromArgb(108, 117, 125);
            lblLegend.Location = new Point(10, panel.Height - 25);
            lblLegend.AutoSize = true;
            panel.Controls.Add(lblLegend);
            
            return panel;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtRoomName.Text))
                {
                    MessageBox.Show("Vui lòng nhập tên phòng!", "Thông báo",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRoomName.Focus();
                    return;
                }

                if (roomService.IsRoomNameExists(txtRoomName.Text.Trim()))
                {
                    MessageBox.Show("Tên phòng đã tồn tại!", "Thông báo",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRoomName.Focus();
                    return;
                }

                // Calculate total capacity
                int totalCapacity = roomSections.Sum(s => s.seat_per_row * s.num_rows);

                // Create room object
                var newRoom = new room
                {
                    room_id = Guid.NewGuid(),
                    room_name = txtRoomName.Text.Trim(),
                    description = txtDescription.Text.Trim(),
                    capacity = totalCapacity,
                    status = cmbStatus.SelectedItem?.ToString() ?? "Hoạt động",
                    created_at = DateTime.Now,
                    updated_at = DateTime.Now
                };

                // Save room và room sections
                if (roomService.AddRoomWithSections(newRoom, roomSections))
                {
                    MessageBox.Show("Thêm phòng thành công!", "Thông báo",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Có lỗi xảy ra khi thêm phòng!", "Lỗi",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi: {ex.Message}", "Lỗi",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            roomService?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
