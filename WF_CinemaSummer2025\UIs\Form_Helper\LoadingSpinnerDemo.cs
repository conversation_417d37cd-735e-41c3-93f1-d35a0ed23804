using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using WF_CinemaSummer2025.UIs.Form_Helper;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Demo form để test custom loading spinner
    /// </summary>
    public partial class LoadingSpinnerDemo : Form
    {
        private CustomLoadingPanel loadingPanel;
        private Button btnTest1, btnTest2, btnTest3, btnTest4;
        private TextBox txtUsername, txtPassword;
        private Label lblResult;

        public LoadingSpinnerDemo()
        {
            InitializeComponent();
            SetupDemo();
        }

        private void SetupDemo()
        {
            this.Text = "Loading Spinner Demo";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 242, 245);

            // Create loading panel
            loadingPanel = new CustomLoadingPanel
            {
                Location = new Point(175, 180),
                Size = new Size(250, 140)
            };
            this.Controls.Add(loadingPanel);

            // Create demo buttons
            btnTest1 = new Button
            {
                Text = "Test Loading (3s)",
                Location = new Point(50, 50),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnTest1.Click += BtnTest1_Click;

            btnTest2 = new Button
            {
                Text = "Test Multi-Step Loading",
                Location = new Point(220, 50),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnTest2.Click += BtnTest2_Click;

            btnTest3 = new Button
            {
                Text = "Test Login Simulation",
                Location = new Point(390, 50),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnTest3.Click += BtnTest3_Click;

            btnTest4 = new Button
            {
                Text = "Test Error Handling",
                Location = new Point(135, 110),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnTest4.Click += BtnTest4_Click;

            // Create input fields for login test
            var lblUser = new Label
            {
                Text = "Username:",
                Location = new Point(50, 170),
                Size = new Size(80, 20)
            };

            txtUsername = new TextBox
            {
                Location = new Point(50, 195),
                Size = new Size(120, 25),
                Text = "admin"
            };

            var lblPass = new Label
            {
                Text = "Password:",
                Location = new Point(50, 230),
                Size = new Size(80, 20)
            };

            txtPassword = new TextBox
            {
                Location = new Point(50, 255),
                Size = new Size(120, 25),
                Text = "123456",
                UseSystemPasswordChar = true
            };

            // Result label
            lblResult = new Label
            {
                Location = new Point(50, 350),
                Size = new Size(500, 100),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "Kết quả sẽ hiển thị ở đây...",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                btnTest1, btnTest2, btnTest3, btnTest4,
                lblUser, txtUsername, lblPass, txtPassword,
                lblResult
            });
        }

        private async void BtnTest1_Click(object sender, EventArgs e)
        {
            DisableButtons();
            
            try
            {
                loadingPanel.StartLoading("Đang tải dữ liệu...");
                
                await Task.Delay(3000);
                
                loadingPanel.LoadingText = "Hoàn thành!";
                await Task.Delay(800);
                
                lblResult.Text = "✅ Test 1 hoàn thành: Loading đơn giản trong 3 giây";
                lblResult.ForeColor = Color.FromArgb(46, 204, 113);
            }
            finally
            {
                loadingPanel.StopLoading();
                EnableButtons();
            }
        }

        private async void BtnTest2_Click(object sender, EventArgs e)
        {
            DisableButtons();
            
            try
            {
                loadingPanel.StartLoading("Bước 1: Chuẩn bị dữ liệu...");
                await Task.Delay(1500);
                
                loadingPanel.LoadingText = "Bước 2: Xử lý dữ liệu...";
                await Task.Delay(2000);
                
                loadingPanel.LoadingText = "Bước 3: Lưu kết quả...";
                await Task.Delay(1500);
                
                loadingPanel.LoadingText = "Hoàn thành!";
                await Task.Delay(800);
                
                lblResult.Text = "✅ Test 2 hoàn thành: Multi-step loading với 3 bước";
                lblResult.ForeColor = Color.FromArgb(46, 204, 113);
            }
            finally
            {
                loadingPanel.StopLoading();
                EnableButtons();
            }
        }

        private async void BtnTest3_Click(object sender, EventArgs e)
        {
            DisableButtons();
            
            try
            {
                string username = txtUsername.Text;
                string password = txtPassword.Text;
                
                // Disable input fields
                txtUsername.ReadOnly = true;
                txtPassword.ReadOnly = true;
                txtUsername.BackColor = Color.FromArgb(248, 249, 250);
                txtPassword.BackColor = Color.FromArgb(248, 249, 250);
                
                loadingPanel.StartLoading("Đang xác thực...");
                await Task.Delay(2000);
                
                // Simulate authentication
                bool loginSuccess = (username == "admin" && password == "123456");
                
                if (loginSuccess)
                {
                    loadingPanel.LoadingText = "Đăng nhập thành công!";
                    await Task.Delay(800);
                    
                    lblResult.Text = $"✅ Đăng nhập thành công!\nChào mừng {username}";
                    lblResult.ForeColor = Color.FromArgb(46, 204, 113);
                }
                else
                {
                    loadingPanel.LoadingText = "Đăng nhập thất bại!";
                    await Task.Delay(800);
                    
                    lblResult.Text = "❌ Đăng nhập thất bại!\nTên đăng nhập hoặc mật khẩu không đúng";
                    lblResult.ForeColor = Color.FromArgb(231, 76, 60);
                }
            }
            finally
            {
                loadingPanel.StopLoading();
                
                // Restore input fields
                txtUsername.ReadOnly = false;
                txtPassword.ReadOnly = false;
                txtUsername.BackColor = Color.White;
                txtPassword.BackColor = Color.White;
                
                EnableButtons();
            }
        }

        private async void BtnTest4_Click(object sender, EventArgs e)
        {
            DisableButtons();
            
            try
            {
                loadingPanel.StartLoading("Đang xử lý yêu cầu...");
                await Task.Delay(2000);
                
                // Simulate error
                throw new Exception("Lỗi kết nối mạng!");
            }
            catch (Exception ex)
            {
                loadingPanel.LoadingText = "Có lỗi xảy ra!";
                await Task.Delay(800);
                
                lblResult.Text = $"❌ Test 4: Xử lý lỗi\nLỗi: {ex.Message}";
                lblResult.ForeColor = Color.FromArgb(231, 76, 60);
            }
            finally
            {
                loadingPanel.StopLoading();
                EnableButtons();
            }
        }

        private void DisableButtons()
        {
            btnTest1.Enabled = false;
            btnTest2.Enabled = false;
            btnTest3.Enabled = false;
            btnTest4.Enabled = false;
        }

        private void EnableButtons()
        {
            btnTest1.Enabled = true;
            btnTest2.Enabled = true;
            btnTest3.Enabled = true;
            btnTest4.Enabled = true;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // LoadingSpinnerDemo
            // 
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.Name = "LoadingSpinnerDemo";
            this.Text = "Loading Spinner Demo";
            this.ResumeLayout(false);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                loadingPanel?.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// Program để chạy demo
    /// </summary>
    public class LoadingSpinnerDemoProgram
    {
        [STAThread]
        public static void RunDemo()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new LoadingSpinnerDemo());
        }
    }
}
