﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using WF_CinemaSummer2025.UIs.Auth;
using WF_CinemaSummer2025.UIs;

namespace WF_CinemaSummer2025
{
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Hiển thị form đăng nhập
            using (var loginForm = new LoginForm())
            {
                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    // Đăng nhập thành công, mở form ch<PERSON>h với thông tin user
                    Application.Run(new MainForm(loginForm.LoggedInUser));
                }
                // Nếu không đăng nhập thành công, ứng dụng sẽ thoát
            }
        }
    }
}
