{"RootPath": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\WF_CinemaSummer2025", "ProjectFileName": "WF_CinemaSummer2025.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Repositories\\customer.cs"}, {"SourceFile": "Repositories\\DatabaseModel.Context.cs"}, {"SourceFile": "Repositories\\DatabaseModel.cs"}, {"SourceFile": "Repositories\\DatabaseModel.Designer.cs"}, {"SourceFile": "Repositories\\movie_attributes.cs"}, {"SourceFile": "Repositories\\movie_types.cs"}, {"SourceFile": "Repositories\\movy.cs"}, {"SourceFile": "Repositories\\price.cs"}, {"SourceFile": "Repositories\\room.cs"}, {"SourceFile": "Repositories\\room_sections.cs"}, {"SourceFile": "Repositories\\schedule.cs"}, {"SourceFile": "Repositories\\sp_helpdiagramdefinition_Result.cs"}, {"SourceFile": "Repositories\\sp_helpdiagrams_Result.cs"}, {"SourceFile": "Repositories\\sysdiagram.cs"}, {"SourceFile": "Repositories\\ticket.cs"}, {"SourceFile": "Repositories\\type.cs"}, {"SourceFile": "Repositories\\user.cs"}, {"SourceFile": "Services\\DatabaseSeeder.cs"}, {"SourceFile": "Services\\RoomService.cs"}, {"SourceFile": "Services\\UserService.cs"}, {"SourceFile": "UIs\\Auth\\LoginForm.cs"}, {"SourceFile": "UIs\\Auth\\LoginForm.Designer.cs"}, {"SourceFile": "UIs\\Form_Helper\\example.cs"}, {"SourceFile": "UIs\\Form_Helper\\MessageForm.cs"}, {"SourceFile": "UIs\\Form_Helper\\MessageForm.Designer.cs"}, {"SourceFile": "UIs\\MainForm.cs"}, {"SourceFile": "UIs\\MainForm.Designer.cs"}, {"SourceFile": "UIs\\Room\\AddRoomForm.cs"}, {"SourceFile": "UIs\\Room\\AddRoomForm.Designer.cs"}, {"SourceFile": "UIs\\Room\\RoomListForm.cs"}, {"SourceFile": "UIs\\Room\\RoomListForm.Designer.cs"}, {"SourceFile": "UIs\\Room\\RoomListUserControl.cs"}, {"SourceFile": "UIs\\Room\\RoomListUserControl.Designer.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\BCrypt.Net-Next.4.0.3\\lib\\net48\\BCrypt.Net-Next.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\EntityFramework.6.5.1\\lib\\net45\\EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\EntityFramework.6.5.1\\lib\\net45\\EntityFramework.SqlServer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\Guna.UI2.WinForms.2.0.4.7\\lib\\net48\\Guna.UI2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\System.Memory.4.5.4\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\System.Numerics.Vectors.4.5.0\\lib\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\packages\\System.Runtime.CompilerServices.Unsafe.4.5.3\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\WF_CinemaSummer2025\\bin\\Debug\\WF_CinemaSummer2025.exe", "OutputItemRelativePath": "WF_CinemaSummer2025.exe"}, {"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\winform\\wf_cinema\\WF_CinemaSummer2025\\bin\\Debug\\WF_CinemaSummer2025.pdb", "OutputItemRelativePath": "WF_CinemaSummer2025.pdb"}], "CopyToOutputEntries": []}