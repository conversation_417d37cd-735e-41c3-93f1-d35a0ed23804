using System;
using System.Drawing;
using System.Windows.Forms;
using WF_CinemaSummer2025.UIs.Form_Helper;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Demo form để test ModernMessageForm theo thiết kế trong hình
    /// </summary>
    public partial class ModernMessageDemo : Form
    {
        private Button btnTestWarning, btnTestSuccess, btnTestError, btnTestQuestion, btnTestInfo;
        private Label lblResult;

        public ModernMessageDemo()
        {
            InitializeComponent();
            SetupDemo();
        }

        private void SetupDemo()
        {
            this.Text = "Modern Message Form Demo";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 247, 250);

            // Create test buttons
            btnTestWarning = CreateButton("Test Warning (Như hình)", Color.FromArgb(255, 193, 7), new Point(50, 50));
            btnTestSuccess = CreateButton("Test Success", Color.FromArgb(40, 167, 69), new Point(250, 50));
            btnTestError = CreateButton("Test Error", Color.FromArgb(220, 53, 69), new Point(450, 50));
            btnTestQuestion = CreateButton("Test Question", Color.FromArgb(52, 152, 219), new Point(50, 120));
            btnTestInfo = CreateButton("Test Information", Color.FromArgb(23, 162, 184), new Point(250, 120));

            // Event handlers
            btnTestWarning.Click += BtnTestWarning_Click;
            btnTestSuccess.Click += BtnTestSuccess_Click;
            btnTestError.Click += BtnTestError_Click;
            btnTestQuestion.Click += BtnTestQuestion_Click;
            btnTestInfo.Click += BtnTestInfo_Click;

            // Result label
            lblResult = new Label
            {
                Location = new Point(50, 200),
                Size = new Size(600, 350),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "Kết quả test sẽ hiển thị ở đây...\n\n" +
                       "🎯 Thiết kế theo hình:\n" +
                       "✓ Icon tròn với màu nền gradient\n" +
                       "✓ Typography đẹp với Segoe UI\n" +
                       "✓ Layout căn giữa hoàn hảo\n" +
                       "✓ Nút bo tròn với màu sắc phân biệt\n" +
                       "✓ Shadow effect chuyên nghiệp\n" +
                       "✓ Responsive design\n\n" +
                       "📱 Tính năng:\n" +
                       "• Warning: Icon vàng với '!' - 2 nút (Không/Có, Tôi chắc chắn)\n" +
                       "• Success: Icon xanh với '✓' - 1 nút (OK)\n" +
                       "• Error: Icon đỏ với '✕' - 1 nút (OK)\n" +
                       "• Question: Icon xanh với '?' - 2 nút (Không/Có)\n" +
                       "• Information: Icon xanh với 'i' - 1 nút (OK)",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(64, 64, 64),
                Padding = new Padding(15)
            };

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                btnTestWarning, btnTestSuccess, btnTestError, btnTestQuestion, btnTestInfo, lblResult
            });
        }

        private Button CreateButton(string text, Color color, Point location)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(180, 50),
                BackColor = color,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void BtnTestWarning_Click(object sender, EventArgs e)
        {
            var result = ModernMessageForm.ShowWarning(
                "Bạn có chắc chắn muốn thực hiện hành động này không? Hành động này không thể được hoàn tác.",
                "Xác nhận Hành động"
            );
            
            lblResult.Text = $"✅ Test Warning hoàn thành (Giống hình mẫu)\n\n" +
                           $"Kết quả: {result}\n\n" +
                           "🎨 Thiết kế:\n" +
                           "• Icon tròn màu vàng với dấu '!' trắng\n" +
                           "• Title: 'Xác nhận Hành động' (18pt, Bold)\n" +
                           "• Message: Màu xám nhạt (12pt)\n" +
                           "• Nút 'Không': Viền xám, nền trắng\n" +
                           "• Nút 'Có, Tôi chắc chắn': Nền đỏ, chữ trắng\n" +
                           "• Layout căn giữa hoàn hảo\n" +
                           "• Shadow effect mượt mà";
            lblResult.ForeColor = Color.FromArgb(255, 193, 7);
        }

        private void BtnTestSuccess_Click(object sender, EventArgs e)
        {
            var result = ModernMessageForm.ShowSuccess(
                "Dữ liệu đã được lưu thành công!\n\nTất cả thay đổi đã được cập nhật vào hệ thống.",
                "Thành công"
            );
            
            lblResult.Text = $"✅ Test Success hoàn thành\n\n" +
                           $"Kết quả: {result}\n\n" +
                           "🎨 Thiết kế:\n" +
                           "• Icon tròn màu xanh lá với dấu '✓' trắng\n" +
                           "• Title: 'Thành công'\n" +
                           "• Chỉ có 1 nút 'OK' màu xanh dương\n" +
                           "• Căn giữa hoàn hảo";
            lblResult.ForeColor = Color.FromArgb(40, 167, 69);
        }

        private void BtnTestError_Click(object sender, EventArgs e)
        {
            var result = ModernMessageForm.ShowError(
                "Không thể kết nối đến cơ sở dữ liệu!\n\nVui lòng kiểm tra kết nối mạng và thử lại.",
                "Lỗi"
            );
            
            lblResult.Text = $"✅ Test Error hoàn thành\n\n" +
                           $"Kết quả: {result}\n\n" +
                           "🎨 Thiết kế:\n" +
                           "• Icon tròn màu đỏ với dấu '✕' trắng\n" +
                           "• Title: 'Lỗi'\n" +
                           "• Chỉ có 1 nút 'OK' màu xanh dương\n" +
                           "• Căn giữa hoàn hảo";
            lblResult.ForeColor = Color.FromArgb(220, 53, 69);
        }

        private void BtnTestQuestion_Click(object sender, EventArgs e)
        {
            var result = ModernMessageForm.ShowQuestion(
                "Bạn có muốn lưu thay đổi trước khi thoát không?\n\nDữ liệu chưa lưu sẽ bị mất.",
                "Xác nhận"
            );
            
            lblResult.Text = $"✅ Test Question hoàn thành\n\n" +
                           $"Kết quả: {result}\n\n" +
                           "🎨 Thiết kế:\n" +
                           "• Icon tròn màu xanh dương với dấu '?' trắng\n" +
                           "• Title: 'Xác nhận'\n" +
                           "• 2 nút: 'Không' (viền) và 'Có' (đỏ)\n" +
                           "• Layout cân đối";
            lblResult.ForeColor = Color.FromArgb(52, 152, 219);
        }

        private void BtnTestInfo_Click(object sender, EventArgs e)
        {
            var result = ModernMessageForm.ShowInformation(
                "Hệ thống sẽ được bảo trì từ 2:00 AM đến 4:00 AM.\n\nVui lòng lưu công việc trước thời gian này.",
                "Thông báo"
            );
            
            lblResult.Text = $"✅ Test Information hoàn thành\n\n" +
                           $"Kết quả: {result}\n\n" +
                           "🎨 Thiết kế:\n" +
                           "• Icon tròn màu xanh dương với chữ 'i' trắng\n" +
                           "• Title: 'Thông báo'\n" +
                           "• Chỉ có 1 nút 'OK' màu xanh dương\n" +
                           "• Căn giữa hoàn hảo";
            lblResult.ForeColor = Color.FromArgb(23, 162, 184);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ModernMessageDemo
            // 
            this.ClientSize = new System.Drawing.Size(700, 600);
            this.Name = "ModernMessageDemo";
            this.Text = "Modern Message Demo";
            this.ResumeLayout(false);
        }
    }

    /// <summary>
    /// Program để chạy demo
    /// </summary>
    public class ModernMessageDemoProgram
    {
        [STAThread]
        public static void RunDemo()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new ModernMessageDemo());
        }
    }
}
