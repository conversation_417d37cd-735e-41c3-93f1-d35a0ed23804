using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;
using WF_CinemaSummer2025.UIs.Form_Helper;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Ví dụ về cách sử dụng LoadingHelper
    /// </summary>
    public class LoadingExamples
    {
        private Guna2Panel loadingOverlay;
        private Form parentForm;

        public LoadingExamples(Form form)
        {
            parentForm = form;
            // Tạo loading overlay một lần duy nhất
            loadingOverlay = LoadingHelper.CreateLoadingOverlay(form);
        }

        /// <summary>
        /// Ví dụ 1: Loading đơn giản
        /// </summary>
        public async Task Example1_SimpleLoading()
        {
            // Hiển thị loading
            LoadingHelper.ShowLoading(loadingOverlay, "Đang tải dữ liệu...");

            try
            {
                // Simulate work
                await Task.Delay(3000);
                
                // Update loading text
                LoadingHelper.UpdateLoadingText(loadingOverlay, "Hoàn thành!");
                await Task.Delay(800);
            }
            finally
            {
                // Ẩn loading
                LoadingHelper.HideLoading(loadingOverlay);
            }
        }

        /// <summary>
        /// Ví dụ 2: Sử dụng ExecuteWithLoadingAsync
        /// </summary>
        public async Task Example2_ExecuteWithLoading()
        {
            await LoadingHelper.ExecuteWithLoadingAsync(
                loadingOverlay,
                async () =>
                {
                    // Simulate API call
                    await Task.Delay(2000);
                    // Thực hiện logic business
                },
                "Đang lưu dữ liệu...",
                "Lưu thành công!"
            );
        }

        /// <summary>
        /// Ví dụ 3: Loading với return value
        /// </summary>
        public async Task<string> Example3_LoadingWithResult()
        {
            var result = await LoadingHelper.ExecuteWithLoadingAsync(
                loadingOverlay,
                async () =>
                {
                    // Simulate API call
                    await Task.Delay(2000);
                    return "Dữ liệu đã được tải thành công!";
                },
                "Đang tải...",
                "Tải hoàn thành!"
            );

            return result;
        }

        /// <summary>
        /// Ví dụ 4: Loading cho form đăng nhập
        /// </summary>
        public async Task<bool> Example4_LoginWithLoading(string username, string password)
        {
            try
            {
                var loginResult = await LoadingHelper.ExecuteWithLoadingAsync(
                    loadingOverlay,
                    async () =>
                    {
                        // Simulate authentication
                        await Task.Delay(1500);
                        
                        // Simulate login logic
                        if (username == "admin" && password == "123")
                        {
                            return true;
                        }
                        return false;
                    },
                    "Đang đăng nhập...",
                    "Đăng nhập thành công!"
                );

                return loginResult;
            }
            catch (Exception)
            {
                LoadingHelper.HideLoading(loadingOverlay);
                throw;
            }
        }

        /// <summary>
        /// Ví dụ 5: Loading cho việc xóa dữ liệu
        /// </summary>
        public async Task Example5_DeleteWithLoading(int itemId)
        {
            await LoadingHelper.ExecuteWithLoadingAsync(
                loadingOverlay,
                async () =>
                {
                    // Simulate delete operation
                    await Task.Delay(1000);
                    
                    // Simulate delete logic
                    // DeleteItem(itemId);
                },
                "Đang xóa...",
                "Xóa thành công!"
            );
        }

        /// <summary>
        /// Ví dụ 6: Loading với nhiều bước
        /// </summary>
        public async Task Example6_MultiStepLoading()
        {
            LoadingHelper.ShowLoading(loadingOverlay, "Bước 1: Chuẩn bị dữ liệu...");
            
            try
            {
                // Bước 1
                await Task.Delay(1000);
                
                // Bước 2
                LoadingHelper.UpdateLoadingText(loadingOverlay, "Bước 2: Xử lý dữ liệu...");
                await Task.Delay(1500);
                
                // Bước 3
                LoadingHelper.UpdateLoadingText(loadingOverlay, "Bước 3: Lưu kết quả...");
                await Task.Delay(1000);
                
                // Hoàn thành
                LoadingHelper.UpdateLoadingText(loadingOverlay, "Hoàn thành!");
                await Task.Delay(800);
            }
            finally
            {
                LoadingHelper.HideLoading(loadingOverlay);
            }
        }

        /// <summary>
        /// Ví dụ 7: Sử dụng trong button click event
        /// </summary>
        public async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                await LoadingHelper.ExecuteWithLoadingAsync(
                    loadingOverlay,
                    async () =>
                    {
                        // Validate data
                        await Task.Delay(500);
                        
                        // Save to database
                        await Task.Delay(2000);
                        
                        // Update UI
                        await Task.Delay(300);
                    },
                    "Đang lưu dữ liệu...",
                    "Lưu thành công!"
                );

                // Show success message
                messageForm.ShowSuccess("Dữ liệu đã được lưu thành công!");
            }
            catch (Exception ex)
            {
                // Show error message
                messageForm.ShowError($"Lỗi khi lưu dữ liệu: {ex.Message}");
            }
        }

        /// <summary>
        /// Ví dụ 8: Loading cho việc tải danh sách
        /// </summary>
        public async Task<object[]> Example8_LoadListWithLoading()
        {
            return await LoadingHelper.ExecuteWithLoadingAsync(
                loadingOverlay,
                async () =>
                {
                    // Simulate loading from database
                    await Task.Delay(2000);
                    
                    // Return mock data
                    return new object[] { "Item 1", "Item 2", "Item 3" };
                },
                "Đang tải danh sách...",
                "Tải hoàn thành!"
            );
        }

        /// <summary>
        /// Cleanup khi form đóng
        /// </summary>
        public void Dispose()
        {
            loadingOverlay?.Dispose();
        }
    }

    /// <summary>
    /// Extension methods để sử dụng LoadingHelper dễ dàng hơn
    /// </summary>
    public static class FormLoadingExtensions
    {
        /// <summary>
        /// Thêm loading capability cho form
        /// </summary>
        /// <param name="form">Form cần thêm loading</param>
        /// <returns>Loading overlay panel</returns>
        public static Guna2Panel AddLoadingCapability(this Form form)
        {
            return LoadingHelper.CreateLoadingOverlay(form);
        }

        /// <summary>
        /// Thực hiện task với loading cho form
        /// </summary>
        /// <param name="form">Form</param>
        /// <param name="task">Task cần thực hiện</param>
        /// <param name="loadingText">Loading text</param>
        /// <param name="successText">Success text</param>
        /// <returns></returns>
        public static async Task ExecuteWithLoadingAsync(this Form form, Func<Task> task,
            string loadingText = "Đang xử lý...", string successText = "Hoàn thành!")
        {
            var overlay = form.Controls.Find("loadingOverlay", false);
            if (overlay.Length > 0 && overlay[0] is Guna2Panel panel)
            {
                await LoadingHelper.ExecuteWithLoadingAsync(panel, task, loadingText, successText);
            }
        }
    }
}
