﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    public partial class messageForm : Form
    {
        // Enum để định nghĩa kết quả trả về
        public enum MessageResult
        {
            Yes,
            No,
            None
        }

        // Property để lưu kết quả người dùng chọn
        public MessageResult Result { get; private set; } = MessageResult.None;

        // Constructor mặc định
        public messageForm()
        {
            InitializeComponent();
            SetupForm();
        }

        // Constructor với message tùy chỉnh
        public messageForm(string message) : this()
        {
            SetMessage(message);
        }

        // Constructor với message và title tùy chỉnh
        public messageForm(string message, string title) : this(message)
        {
            this.Text = title;
        }

        // Phương thức để thiết lập form
        private void SetupForm()
        {
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.TopMost = true;
        }

        // Phương thức để set message
        public void SetMessage(string message)
        {
            lblMessage.Text = message;
        }

        // Event handler cho button "Có"
        private void btnYes_Click(object sender, EventArgs e)
        {
            Result = MessageResult.Yes;
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        // Event handler cho button "Không"
        private void btnNo_Click(object sender, EventArgs e)
        {
            Result = MessageResult.No;
            this.DialogResult = DialogResult.No;
            this.Close();
        }

        // Phương thức static để hiển thị message box một cách dễ dàng
        public static MessageResult ShowMessage(string message, string title = "Thông báo")
        {
            using (var form = new messageForm(message, title))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        // Phương thức static để hiển thị message box với parent form
        public static MessageResult ShowMessage(IWin32Window owner, string message, string title = "Thông báo")
        {
            using (var form = new messageForm(message, title))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }
    }
}
