﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    public partial class messageForm : Form
    {
        // Enum để định nghĩa loại message
        public enum MessageType
        {
            Information,    // Thông báo thông thường
            Success,        // Thành công
            Warning,        // Cảnh báo
            E<PERSON>r,          // Lỗi
            Question        // Câu hỏi xác nhận
        }

        // Enum để định nghĩa kết quả trả về
        public enum MessageResult
        {
            Yes,
            No,
            OK,
            None
        }

        // Property để lưu kết quả người dùng chọn
        public MessageResult Result { get; private set; } = MessageResult.None;

        private MessageType _messageType = MessageType.Information;

        // Constructor mặc định
        public messageForm()
        {
            InitializeComponent();
            SetupForm();
        }

        // Constructor với message tùy chỉnh
        public messageForm(string message, MessageType type = MessageType.Information) : this()
        {
            SetMessage(message, type);
        }

        // Constructor với message, type và title tùy chỉnh
        public messageForm(string message, string title, MessageType type = MessageType.Information) : this(message, type)
        {
            this.Text = title;
        }

        // Phương thức để thiết lập form
        private void SetupForm()
        {
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // Thêm shadow effect
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.TransparencyKey = Color.FromArgb(240, 240, 240);
        }

        // Phương thức để set message và type
        public void SetMessage(string message, MessageType type = MessageType.Information)
        {
            lblMessage.Text = message;
            _messageType = type;
            SetupMessageType(type);
        }

        // Phương thức để thiết lập giao diện theo loại message
        private void SetupMessageType(MessageType type)
        {
            switch (type)
            {
                case MessageType.Success:
                    SetupSuccessStyle();
                    break;
                case MessageType.Warning:
                    SetupWarningStyle();
                    break;
                case MessageType.Error:
                    SetupErrorStyle();
                    break;
                case MessageType.Question:
                    SetupQuestionStyle();
                    break;
                default:
                    SetupInformationStyle();
                    break;
            }
        }

        private void SetupSuccessStyle()
        {
            panelHeader.BackColor = Color.FromArgb(40, 167, 69);
            lblTitle.Text = "✓ Thành công";
            lblTitle.ForeColor = Color.White;
            iconPanel.BackColor = Color.FromArgb(40, 167, 69);

            // Chỉ hiển thị nút OK cho success
            btnYes.Visible = false;
            btnNo.Visible = false;
          
        }

        private void SetupWarningStyle()
        {
            panelHeader.BackColor = Color.FromArgb(255, 193, 7);
            lblTitle.Text = "⚠ Cảnh báo";
            lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            iconPanel.BackColor = Color.FromArgb(255, 193, 7);

            // Hiển thị cả hai nút cho warning
            btnYes.Visible = true;
            btnNo.Visible = true;
            
        }

        private void SetupErrorStyle()
        {
            panelHeader.BackColor = Color.FromArgb(220, 53, 69);
            lblTitle.Text = "✕ Lỗi";
            lblTitle.ForeColor = Color.White;
            iconPanel.BackColor = Color.FromArgb(220, 53, 69);

            // Chỉ hiển thị nút OK cho error
            btnYes.Visible = false;
            btnNo.Visible = false;
            
        }

        private void SetupQuestionStyle()
        {
            panelHeader.BackColor = Color.FromArgb(0, 123, 255);
            lblTitle.Text = "? Xác nhận";
            lblTitle.ForeColor = Color.White;
            iconPanel.BackColor = Color.FromArgb(0, 123, 255);

            // Hiển thị cả hai nút cho question
            btnYes.Visible = true;
            btnNo.Visible = true;
    
        }

        private void SetupInformationStyle()
        {
            panelHeader.BackColor = Color.FromArgb(23, 162, 184);
            lblTitle.Text = "ℹ Thông báo";
            lblTitle.ForeColor = Color.White;
            iconPanel.BackColor = Color.FromArgb(23, 162, 184);

            // Chỉ hiển thị nút OK cho information
            btnYes.Visible = false;
            btnNo.Visible = false;
        }

        // Event handlers
        private void btnYes_Click(object sender, EventArgs e)
        {
            Result = MessageResult.Yes;
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        private void btnNo_Click(object sender, EventArgs e)
        {
            Result = MessageResult.No;
            this.DialogResult = DialogResult.No;
            this.Close();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            Result = MessageResult.OK;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        // Static methods để sử dụng dễ dàng
        public static MessageResult ShowSuccess(string message, string title = "Thành công")
        {
            using (var form = new messageForm(message, title, MessageType.Success))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowError(string message, string title = "Lỗi")
        {
            using (var form = new messageForm(message, title, MessageType.Error))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowWarning(string message, string title = "Cảnh báo")
        {
            using (var form = new messageForm(message, title, MessageType.Warning))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowQuestion(string message, string title = "Xác nhận")
        {
            using (var form = new messageForm(message, title, MessageType.Question))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        public static MessageResult ShowInformation(string message, string title = "Thông báo")
        {
            using (var form = new messageForm(message, title, MessageType.Information))
            {
                form.ShowDialog();
                return form.Result;
            }
        }

        // Overloads với parent form
        public static MessageResult ShowSuccess(IWin32Window owner, string message, string title = "Thành công")
        {
            using (var form = new messageForm(message, title, MessageType.Success))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static MessageResult ShowError(IWin32Window owner, string message, string title = "Lỗi")
        {
            using (var form = new messageForm(message, title, MessageType.Error))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static MessageResult ShowWarning(IWin32Window owner, string message, string title = "Cảnh báo")
        {
            using (var form = new messageForm(message, title, MessageType.Warning))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static MessageResult ShowQuestion(IWin32Window owner, string message, string title = "Xác nhận")
        {
            using (var form = new messageForm(message, title, MessageType.Question))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        public static MessageResult ShowInformation(IWin32Window owner, string message, string title = "Thông báo")
        {
            using (var form = new messageForm(message, title, MessageType.Information))
            {
                form.ShowDialog(owner);
                return form.Result;
            }
        }

        private void guna2Panel1_Paint(object sender, PaintEventArgs e)
        {

        }

        private void iconPanel_Paint(object sender, PaintEventArgs e)
        {

        }

        private void lblTitle_Click(object sender, EventArgs e)
        {

        }
    }
}
