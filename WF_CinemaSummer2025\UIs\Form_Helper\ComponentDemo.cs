using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using WF_CinemaSummer2025.UIs.Form_Helper;

namespace WF_CinemaSummer2025.UIs.Form_Helper
{
    /// <summary>
    /// Demo form để test các component: errorForm, messageForm, SmoothLoadingSpinner
    /// </summary>
    public partial class ComponentDemo : Form
    {
        private SmoothLoadingPanel loadingPanel;
        private Button btnTestError, btnTestWarning, btnTestInfo, btnTestCritical;
        private Button btnTestMessage, btnTestLoading;
        private Label lblResult;

        public ComponentDemo()
        {
            InitializeComponent();
            SetupDemo();
        }

        private void SetupDemo()
        {
            this.Text = "Component Demo - Error Form & Loading Spinner";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(245, 247, 250);

            // Create loading panel
            loadingPanel = new SmoothLoadingPanel
            {
                Location = new Point(225, 200),
                Size = new Size(250, 140)
            };
            this.Controls.Add(loadingPanel);

            // Create demo buttons for errorForm
            btnTestError = CreateButton("Test Error", Color.FromArgb(220, 53, 69), new Point(50, 50));
            btnTestWarning = CreateButton("Test Warning", Color.FromArgb(255, 193, 7), new Point(200, 50));
            btnTestInfo = CreateButton("Test Information", Color.FromArgb(23, 162, 184), new Point(350, 50));
            btnTestCritical = CreateButton("Test Critical", Color.FromArgb(139, 0, 0), new Point(500, 50));

            // Create demo buttons for other components
            btnTestMessage = CreateButton("Test Message", Color.FromArgb(40, 167, 69), new Point(50, 120));
            btnTestLoading = CreateButton("Test Loading", Color.FromArgb(52, 152, 219), new Point(200, 120));

            // Event handlers
            btnTestError.Click += BtnTestError_Click;
            btnTestWarning.Click += BtnTestWarning_Click;
            btnTestInfo.Click += BtnTestInfo_Click;
            btnTestCritical.Click += BtnTestCritical_Click;
            btnTestMessage.Click += BtnTestMessage_Click;
            btnTestLoading.Click += BtnTestLoading_Click;

            // Result label
            lblResult = new Label
            {
                Location = new Point(50, 400),
                Size = new Size(600, 150),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "Kết quả test sẽ hiển thị ở đây...\n\nHướng dẫn:\n- Test Error: Hiển thị lỗi với nút OK\n- Test Warning: Hiển thị cảnh báo với nút OK/Cancel\n- Test Information: Hiển thị thông tin với nút OK\n- Test Critical: Hiển thị lỗi nghiêm trọng với nút Retry/Cancel",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(64, 64, 64),
                Padding = new Padding(10)
            };

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                btnTestError, btnTestWarning, btnTestInfo, btnTestCritical,
                btnTestMessage, btnTestLoading, lblResult
            });
        }

        private Button CreateButton(string text, Color color, Point location)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(130, 40),
                BackColor = color,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                FlatAppearance = { BorderSize = 0 }
            };
        }

        private void BtnTestError_Click(object sender, EventArgs e)
        {
            var result = errorForm.ShowError(
                "Không thể kết nối đến cơ sở dữ liệu!\n\nVui lòng kiểm tra kết nối mạng và thử lại.",
                "Lỗi kết nối"
            );
            
            lblResult.Text = $"✅ Test Error hoàn thành\nKết quả: {result}\nIcon ✕ hiển thị không có viền vuông";
            lblResult.ForeColor = Color.FromArgb(220, 53, 69);
        }

        private void BtnTestWarning_Click(object sender, EventArgs e)
        {
            var result = errorForm.ShowWarning(
                "Bạn đang thực hiện thao tác có thể ảnh hưởng đến dữ liệu quan trọng.\n\nBạn có chắc chắn muốn tiếp tục?",
                "Cảnh báo quan trọng"
            );
            
            lblResult.Text = $"✅ Test Warning hoàn thành\nKết quả: {result}\nIcon ⚠ hiển thị không có viền vuông";
            lblResult.ForeColor = Color.FromArgb(255, 193, 7);
        }

        private void BtnTestInfo_Click(object sender, EventArgs e)
        {
            var result = errorForm.ShowInformation(
                "Hệ thống sẽ được bảo trì từ 2:00 AM đến 4:00 AM.\n\nVui lòng lưu công việc trước thời gian này.",
                "Thông báo bảo trì"
            );
            
            lblResult.Text = $"✅ Test Information hoàn thành\nKết quả: {result}\nIcon ℹ hiển thị không có viền vuông";
            lblResult.ForeColor = Color.FromArgb(23, 162, 184);
        }

        private void BtnTestCritical_Click(object sender, EventArgs e)
        {
            var result = errorForm.ShowCritical(
                "Lỗi nghiêm trọng trong hệ thống!\n\nDữ liệu có thể bị mất. Vui lòng liên hệ quản trị viên ngay lập tức.",
                "Lỗi nghiêm trọng"
            );
            
            lblResult.Text = $"✅ Test Critical hoàn thành\nKết quả: {result}\nIcon 🚨 hiển thị không có viền vuông";
            lblResult.ForeColor = Color.FromArgb(139, 0, 0);
        }

        private void BtnTestMessage_Click(object sender, EventArgs e)
        {
            var result = messageForm.ShowQuestion(
                "Bạn có muốn lưu thay đổi trước khi thoát không?",
                "Xác nhận"
            );
            
            lblResult.Text = $"✅ Test Message hoàn thành\nKết quả: {result}\nMessageForm hoạt động bình thường";
            lblResult.ForeColor = Color.FromArgb(40, 167, 69);
        }

        private async void BtnTestLoading_Click(object sender, EventArgs e)
        {
            DisableButtons();
            
            try
            {
                loadingPanel.StartLoading("Đang tải dữ liệu...");
                await Task.Delay(2000);
                
                loadingPanel.LoadingText = "Đang xử lý...";
                await Task.Delay(1500);
                
                loadingPanel.LoadingText = "Hoàn thành!";
                await Task.Delay(800);
                
                lblResult.Text = "✅ Test Loading hoàn thành\nSpinner mượt mà, không có viền vuông\nHiệu ứng fade đẹp mắt";
                lblResult.ForeColor = Color.FromArgb(52, 152, 219);
            }
            finally
            {
                loadingPanel.StopLoading();
                EnableButtons();
            }
        }

        private void DisableButtons()
        {
            btnTestError.Enabled = false;
            btnTestWarning.Enabled = false;
            btnTestInfo.Enabled = false;
            btnTestCritical.Enabled = false;
            btnTestMessage.Enabled = false;
            btnTestLoading.Enabled = false;
        }

        private void EnableButtons()
        {
            btnTestError.Enabled = true;
            btnTestWarning.Enabled = true;
            btnTestInfo.Enabled = true;
            btnTestCritical.Enabled = true;
            btnTestMessage.Enabled = true;
            btnTestLoading.Enabled = true;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ComponentDemo
            // 
            this.ClientSize = new System.Drawing.Size(700, 600);
            this.Name = "ComponentDemo";
            this.Text = "Component Demo";
            this.ResumeLayout(false);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                loadingPanel?.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// Program để chạy demo
    /// </summary>
    public class ComponentDemoProgram
    {
        [STAThread]
        public static void RunDemo()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new ComponentDemo());
        }
    }
}
